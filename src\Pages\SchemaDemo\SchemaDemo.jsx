import { useState, useRef } from "react";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, FileText, RefreshCw, Download } from "lucide-react";
import { toast } from "sonner";
import SchemaSelector from "@/components/SchemaSelector/SchemaSelector";
import FormRenderer from "@/components/FormRenderer/FormRenderer";
import { getSchemaByCode, getSchemaMetadata } from "@/schemas";
import { usePrimaryApplicationData } from "@/hooks/usePrimaryApplicationData";

export default function SchemaDemo() {
  const [selectedSchemaCode, setSelectedSchemaCode] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [primaryApplicationId, setPrimaryApplicationId] = useState("");
  const [enableAutoFill, setEnableAutoFill] = useState(false);

  // Ref to store form methods for auto-population
  const formMethodsRef = useRef(null);

  const selectedSchema = selectedSchemaCode
    ? getSchemaByCode(selectedSchemaCode)
    : null;
  const schemaMetadata = selectedSchemaCode
    ? getSchemaMetadata(selectedSchemaCode)
    : null;

  // Use primary application data hook
  const {
    isLoading: isPrimaryDataLoading,
    error: primaryDataError,
    primaryData,
    refetch: refetchPrimaryData,
    transformedData,
  } = usePrimaryApplicationData(
    primaryApplicationId,
    formMethodsRef.current,
    enableAutoFill && !!primaryApplicationId,
    selectedSchema // Pass the schema for enhanced mapping
  );

  const handleSchemaSelect = (schemaCode) => {
    setSelectedSchemaCode(schemaCode);
  };

  const handleProceedToForm = () => {
    if (selectedSchemaCode) {
      setShowForm(true);
    }
  };

  const handleBackToSelection = () => {
    setShowForm(false);
    setPrimaryApplicationId("");
    setEnableAutoFill(false);
  };

  const handleFormSubmit = (data) => {
    console.log("Form submitted with data:", data);
    toast.success("Application submitted successfully!", {
      description: `Your ${schemaMetadata?.name} application has been received.`,
    });
  };

  const handleAddApplicant = (data, reset) => {
    console.log("Adding applicant:", data);
    toast.success("Applicant added successfully!");
    reset();
  };

  const handleLoadPrimaryData = () => {
    if (!primaryApplicationId.trim()) {
      toast.error("Please enter a Primary Application ID");
      return;
    }
    setEnableAutoFill(true);
    toast.info("Loading primary application data...");
  };

  const handleRefreshData = () => {
    if (primaryApplicationId && enableAutoFill) {
      refetchPrimaryData();
      toast.info("Refreshing primary application data...");
    }
  };

  if (showForm && selectedSchema) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="w-full max-w-none mx-auto px-6 py-8">
          {/* Header */}
          <div className="mb-12">
            <div className="bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-3xl p-8 relative overflow-hidden shadow-2xl">
              {/* Decorative background elements */}
              <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 translate-x-20"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/5 rounded-full translate-y-16 -translate-x-16"></div>
              <div className="absolute top-1/2 right-1/4 w-6 h-6 bg-white/20 rounded-full"></div>
              <div className="absolute top-1/4 right-1/3 w-4 h-4 bg-white/15 rounded-full"></div>
              <div className="absolute bottom-1/4 right-1/2 w-3 h-3 bg-white/10 rounded-full"></div>

              {/* Grid pattern overlay */}
              <div className="absolute inset-0 opacity-10">
                <div className="grid grid-cols-12 gap-4 h-full">
                  {Array.from({ length: 12 }).map((_, i) => (
                    <div
                      key={i}
                      className="border-r border-white/20 last:border-r-0"
                    ></div>
                  ))}
                </div>
              </div>

              <div className="relative z-10">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                  <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={handleBackToSelection}
                      className="flex items-center gap-2 self-start bg-white/90 backdrop-blur-sm hover:bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 text-gray-700 hover:text-gray-900"
                    >
                      <ArrowLeft className="h-5 w-5" />
                      Back to Selection
                    </Button>
                    <div className="flex items-center gap-6">
                      <div className="p-6 bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl relative border border-white/20">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/50 rounded-3xl"></div>
                        <FileText className="h-14 w-14 text-blue-600 relative z-10" />
                      </div>
                      <div>
                        <CardTitle className="text-5xl font-black text-white mb-4 tracking-tight">
                          {schemaMetadata?.title}
                        </CardTitle>
                        <p className="text-blue-100 text-xl font-medium mb-6 leading-relaxed max-w-2xl">
                          {schemaMetadata?.description}
                        </p>
                        <div className="flex items-center gap-4">
                          <Badge className="text-base font-bold px-6 py-3 bg-white/20 backdrop-blur-sm text-white border border-white/30 shadow-lg">
                            {selectedSchemaCode}
                          </Badge>
                          <div className="h-6 w-px bg-white/30"></div>
                          <span className="text-base text-blue-100 font-semibold">
                            Secure Application Portal
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-3">
                    <div className="flex items-center gap-2 text-white/80">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium">
                        Secure & Encrypted
                      </span>
                    </div>
                    <div className="text-xs text-white/60 font-medium">
                      SSL Protected Application
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Primary Application Auto-Fill Section */}
          <div
            className="bg-white rounded-lg p-6 mt-4"
            style={{
              border: "none",
              boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
            }}
          >
            <div className="flex flex-col lg:flex-row lg:items-end gap-4">
              <div className="flex-1">
                <Label
                  htmlFor="primaryAppId"
                  className="text-sm font-medium text-gray-700 mb-2 block"
                >
                  Auto-fill from Primary Application
                </Label>
                <div className="flex gap-2">
                  <Input
                    id="primaryAppId"
                    placeholder="Enter Primary Application ID"
                    value={primaryApplicationId}
                    onChange={(e) => setPrimaryApplicationId(e.target.value)}
                    className="flex-1"
                    disabled={isPrimaryDataLoading}
                  />
                  <Button
                    onClick={handleLoadPrimaryData}
                    disabled={
                      isPrimaryDataLoading || !primaryApplicationId.trim()
                    }
                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 border-0 shadow-sm"
                  >
                    {isPrimaryDataLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Download className="h-4 w-4" />
                    )}
                    Load Data
                  </Button>
                  {enableAutoFill && (
                    <Button
                      variant="outline"
                      onClick={handleRefreshData}
                      disabled={isPrimaryDataLoading}
                      className="flex items-center gap-2 border-0 bg-gray-50 hover:bg-gray-100 shadow-sm"
                    >
                      <RefreshCw className="h-4 w-4" />
                      Refresh
                    </Button>
                  )}
                </div>
              </div>
              {enableAutoFill && primaryData && (
                <div className="text-sm text-green-600 font-medium">
                  ✓ Data loaded successfully
                </div>
              )}
              {primaryDataError && (
                <div className="text-sm text-red-600 font-medium">
                  ✗ {primaryDataError}
                </div>
              )}
            </div>
            {transformedData && (
              <div className="mt-3 text-xs text-gray-500">
                Auto-filled: {Object.keys(transformedData).length} fields
              </div>
            )}
          </div>

          {/* Form */}
          <FormRenderer
            schema={selectedSchema}
            defaultValues={transformedData || {}}
            onSubmit={handleFormSubmit}
            onAddApplicant={handleAddApplicant}
            isAdditional={false}
            showHeader={false} // We're showing our own header
            formMethodsRef={formMethodsRef}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <SchemaSelector
        onSchemaSelect={handleSchemaSelect}
        selectedSchema={selectedSchemaCode}
      />

      {/* Proceed Button */}
      {selectedSchemaCode && (
        <div className="w-full max-w-none mx-auto px-6 pb-8">
          <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 shadow-lg">
            <CardContent className="p-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div>
                  <h3 className="text-2xl font-bold text-green-800 mb-2">
                    Ready to proceed with {schemaMetadata?.name}?
                  </h3>
                  <p className="text-green-700 text-base leading-relaxed">
                    You&apos;ve selected the {schemaMetadata?.name} application.
                    Click below to start filling out your application form and
                    take the next step towards your financial goals.
                  </p>
                </div>
                <Button
                  onClick={handleProceedToForm}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 px-8 py-3 text-base font-semibold shadow-lg"
                  size="lg"
                >
                  Start Application
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
