import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_HOST } from "../../../constants";
import { getHeaders } from "../../header/requestHeader";
import { setUser, setUserError, setUserLoading } from "../../reducer/authentication/user";

export const userApi = createApi({
    reducerPath: "userApi",
    baseQuery: fetchBaseQuery({
        baseUrl: `${API_HOST}`,
    }),

    endpoints: (builder) => ({
        addUser: builder.mutation({
            query: (userData) => ({
                url: `/auth/users`,
                method: "POST",
                body: userData,
                headers: getHeaders(),
            }),
        }),

        getUsers: builder.mutation({
            query: ({ page = 1, limit = 10 }) => ({
                url: `/auth/users?page=${page}&limit=${limit}`,
                method: "GET",
                headers: getHeaders(),
            }),
            async onQueryStarted(_, { dispatch, queryFulfilled }) {
                dispatch(setUserLoading()); // Set loading to true before the request

                try {
                    const response = await queryFulfilled;
                    dispatch(
                        setUser({
                            data: response.data.data.data,
                            currentPage: Number(response.data.data.currentPage),
                            totalPages: Number(response.data.data.totalPages),
                            totalBranches: Number(response.data.data.totalItems),
                        })
                    );
                } catch (e) {
                    console.log(">>>>>setUser error", e);
                    dispatch(setUserError(e.message || "Failed to load users"));
                }
            },
        }),

        getSingleUser: builder.mutation({
            query: (id) => ({
                url: `/auth/users/${id}`,
                method: "GET",
                headers: getHeaders(),
            }),
            async onQueryStarted(_, { dispatch, queryFulfilled }) {
                dispatch(setUserLoading()); // Set loading to true before the request

                try {
                    const response = await queryFulfilled;
                    console.log(response.data.data);
                    dispatch(
                        setUser({
                            user: response.data.data,
                        })
                    );
                } catch (e) {
                    console.log(">>>>>setUser error", e);
                    dispatch(setUserError(e.message || "Failed to load user"));
                }
            },
        }),
    }),
});
// Export hooks for usage in functional components
export const { useAddUserMutation, useGetUsersMutation, useGetSingleUserMutation } =
    userApi;
