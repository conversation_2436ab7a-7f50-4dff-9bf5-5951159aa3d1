import { useEffect, useState } from 'react';
import { useGetPrimaryApplicationMutation } from '../store/actions/primaryApplication/primaryApplication';
import { mapPrimaryApplicationToForm } from '../utils/dataMapper';

/**
 * Hook to fetch and transform primary application data for form auto-population
 * @param {string} primaryApplicationId - The ID of the primary application
 * @param {object} methods - React Hook Form methods (reset, setValue, etc.)
 * @param {boolean} enabled - Whether to fetch data automatically
 * @param {object} schema - Form schema for enhanced mapping (optional)
 */
export const usePrimaryApplicationData = (primaryApplicationId, methods, enabled = true, schema = null) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [primaryData, setPrimaryData] = useState(null);

  const [getPrimaryApplication] = useGetPrimaryApplicationMutation();

  // Mapping between primary application fields and home loan schema fields
  const fieldMapping = {
    // Personal Information
    'firstName': 'fname',
    'middleName': 'mname',
    'lastName': 'lname',
    'email': 'email',
    'phoneCode': 'phoneCode',
    'phone': 'phoneNumber',
    'dateOfBirth': 'dateOfBirth',
    'gender': 'gender',
    'maritalStatus': 'maritalStatus',
    'nationality': 'nationality',
    'motherName': 'motherName',

    // Address Information
    'address': 'homeAddressLineFirst', // Combine address fields if needed
    'city': 'homeAddressCity',
    'state': 'homeAddressState',
    'pincode': 'homeAddressPincode',
    'country': 'homeAddressCountry',

    // Identification
    'pan': 'panNumber',
    'aadhaar': 'aadhaarNumber', // If available in primary app

    // Employment Information
    'occupation': 'occupationType',
    'employerName': 'employerName',
    'workExperience': 'applicantExperience',
    'monthlyIncome': 'firstBorrowerIncome',

    // Business Address (if applicable)
    'businessAddress': 'businessAddressLineFirst',
    'businessCity': 'businessAddressCity',
    'businessState': 'businessAddressState',
    'businessPincode': 'businessAddressPincode',

    // Loan Information
    'loanAmount': 'loanAmount',
    'loanPurpose': 'purposeOfLoan',
    'loanType': 'typeOfLoan',
  };

  const transformPrimaryDataToFormData = (primaryAppData) => {
    const formData = {};

    // Transform basic fields
    Object.entries(fieldMapping).forEach(([formField, primaryField]) => {
      const value = getNestedValue(primaryAppData, primaryField);
      if (value !== undefined && value !== null && value !== '') {
        formData[formField] = value;
      }
    });

    // Handle complex transformations

    // Combine address fields if they exist separately
    if (primaryAppData.homeAddressLineFirst) {
      const addressParts = [
        primaryAppData.homeAddressLineFirst,
        primaryAppData.homeAddressLineSecond,
        primaryAppData.homeAddressLineThird
      ].filter(Boolean);

      if (addressParts.length > 0) {
        formData.address = addressParts.join(', ');
      }
    }

    // Handle phone number formatting
    if (primaryAppData.phoneNumber) {
      formData.phone = primaryAppData.phoneNumber;
      formData.phoneCode = primaryAppData.phoneCode || '+91';
    }

    // Handle alternate phone if available
    if (primaryAppData.alternatePhoneNumber) {
      formData.alternatePhone = primaryAppData.alternatePhoneNumber;
      formData.alternatePhoneCode = primaryAppData.alternatePhoneCode || '+91';
    }

    // Handle employment type mapping
    if (primaryAppData.occupationType) {
      // Map occupation types to employment types
      const occupationMapping = {
        'salaried': 'employed',
        'self_employed': 'self_employed',
        'business': 'business_owner',
        'professional': 'professional',
        'retired': 'retired',
        'student': 'student',
        'homemaker': 'homemaker'
      };

      formData.employmentType = occupationMapping[primaryAppData.occupationType] || primaryAppData.occupationType;
    }

    // Handle income information
    if (primaryAppData.firstBorrowerIncome) {
      formData.monthlyIncome = parseFloat(primaryAppData.firstBorrowerIncome);
    }

    // Handle loan amount
    if (primaryAppData.loanAmount) {
      formData.loanAmount = parseFloat(primaryAppData.loanAmount);
    }

    // Handle property information if available
    if (primaryAppData.propertyAddress) {
      formData.propertyAddress = primaryAppData.propertyAddress;
    }

    if (primaryAppData.propertyType) {
      formData.propertyType = primaryAppData.propertyType;
    }

    if (primaryAppData.landArea) {
      formData.landArea = parseFloat(primaryAppData.landArea);
    }

    if (primaryAppData.constructionArea) {
      formData.constructionArea = parseFloat(primaryAppData.constructionArea);
    }

    return formData;
  };

  // Helper function to get nested values safely
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((acc, part) => acc?.[part], obj);
  };

  const fetchPrimaryApplicationData = async () => {
    if (!primaryApplicationId || !enabled) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await getPrimaryApplication(primaryApplicationId).unwrap();

      if (response?.data) {
        setPrimaryData(response.data);

        // Transform and populate form using enhanced data mapper
        const transformedData = schema
          ? mapPrimaryApplicationToForm(response.data, schema, {
            includeEmptyFields: true,
            validateData: false,
            customMappings: {
              applicationDate: new Date().toISOString().slice(0, 10),
            }
          })
          : transformPrimaryDataToFormData(response.data);

        // Reset form with transformed data
        if (methods?.reset) {
          methods.reset({
            ...methods.getValues(),
            ...transformedData
          });
        }

        console.log('Primary application data loaded and form populated:', transformedData);
      }
    } catch (err) {
      console.error('Error fetching primary application data:', err);
      setError(err.message || 'Failed to fetch primary application data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPrimaryApplicationData();
  }, [primaryApplicationId, enabled]);

  return {
    isLoading,
    error,
    primaryData,
    refetch: fetchPrimaryApplicationData,
    transformedData: primaryData
      ? (schema
        ? mapPrimaryApplicationToForm(primaryData, schema, {
          includeEmptyFields: true,
          validateData: false,
          customMappings: {
            applicationDate: new Date().toISOString().slice(0, 10),
          }
        })
        : transformPrimaryDataToFormData(primaryData))
      : null
  };
};

export default usePrimaryApplicationData;
