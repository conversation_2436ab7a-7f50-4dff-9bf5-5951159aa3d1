import { useContext, useEffect, useState, useCallback, useRef } from "react";
import { UNSAFE_NavigationContext as NavigationContext } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

export const usePromptWithDialog = (when) => {
  const navigator = useContext(NavigationContext).navigator;
  const [showDialog, setShowDialog] = useState(false);
  const [tx, setTx] = useState(null);

  // Ref to disable prompt temporarily
  const disablePromptRef = useRef(false);

  const cancelNavigation = () => {
    setShowDialog(false);
    setTx(null);
  };

  const confirmNavigation = () => {
    setShowDialog(false);
    localStorage.removeItem("documentDetailsId");
    localStorage.removeItem("collateralDetailsId");
    localStorage.removeItem("loanDetailsId");
    localStorage.removeItem("personalInfoId");
    if (tx) {
      tx.retry();
      setTx(null);
    }
  };

  const blocker = useCallback((tx) => {
    if (disablePromptRef.current) {
      // If prompt disabled, just allow navigation
      tx.retry();
      disablePromptRef.current = false; // reset after use
      return;
    }

    setShowDialog(true);
    setTx(tx);
  }, []);

  useEffect(() => {
    if (!when) return;

    const unblock = navigator.block((tx) => {
      const autoTx = {
        ...tx,
        retry() {
          unblock();
          tx.retry();
        },
      };
      blocker(autoTx);
    });

    return unblock;
  }, [navigator, blocker, when]);

  // Call this function before you trigger navigation on submit
  const disablePromptForNextNavigation = () => {
    disablePromptRef.current = true;
  };

  const DialogComponent = () => (
    <Dialog open={showDialog} onOpenChange={cancelNavigation}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Leave this page?</DialogTitle>
        </DialogHeader>
        <p className="text-sm text-muted-foreground">
          You have unsaved changes. Leaving now will discard them.
        </p>
        <DialogFooter>
          <Button onClick={cancelNavigation}>Stay</Button>
          <Button
            variant="outline"
            className="border-red-500 hover:bg-red-100"
            onClick={confirmNavigation}
          >
            Leave
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  return { DialogComponent, disablePromptForNextNavigation };
};
