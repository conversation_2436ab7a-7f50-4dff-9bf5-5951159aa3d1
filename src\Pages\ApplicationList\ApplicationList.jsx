import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { replacePathParams } from "../../lib/utils";
import { ROUTES } from "../../constants/route";
import {
  useGetListPrimaryApplicationMutation,
  useGetProductMutation,
} from "../../store/actions";
import { useSelector } from "react-redux";
import Loader from "../../components/custom/Loader";
import { setApiError } from "../../hooks/errorMessage";
import CustomPagination from "../../components/custom/Pagination";
import usePagination from "../../hooks/usePagination";
import { dateFormat } from "../../utils/dateTime";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu";
import { useEffect } from "react";

const ApplicationList = () => {
  const [getApplication] = useGetListPrimaryApplicationMutation();
  const [getLoanTypes] = useGetProductMutation();

  useEffect(() => {
    getLoanTypes({ page: 1, limit: 5000000 });
  }, []);

  const { applications, isLoading, totalPages } = useSelector(
    (state) => state.primaryApplication
  );

  const { data } = useSelector((state) => state.product);

  const { page, setPage, limit, setLimit } = usePagination(getApplication, {
    status: 2,
  });

  const navigate = useNavigate();

  // Mapping loan type names to loan codes
  const loanTypeToCodeMap = {
    "Home Loan": "HL",
    "Housing Loan": "HL",
    "Education Loan": "EL",
    "Business Loan": "BL",
    "Personal Loan": "PL",
    "Car Loan": "CL",
    "Gold Loan": "GL",
  };

  const handleSelect = (loanTypeName, applicationId) => {
    // Get the loan code from the loan type name
    const loanCode = loanTypeToCodeMap[loanTypeName] || "HL"; // Default to HL if not found

    navigate(
      replacePathParams(ROUTES.HOUSING_LOAN_FORM, {
        loanCode: loanCode,
        primaryAppId: applicationId,
      })
    );
  };

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className="p-8 flex-col flex-1 h-screen overflow-y-hidden">
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <div className="flex flex-col gap-4">
          {/* <div className="flex flex-1 justify-end">
            <Button onClick={() => handleAdd()}>Add Branch</Button>
          </div> */}
          <Table>
            <TableHeader>
              <TableRow className="border-0 bg-gradient-to-r from-gray-50 to-gray-100/80 hover:from-gray-100 hover:to-gray-200/80 transition-all duration-200">
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Application Code
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Name
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Email
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Date of Birth
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Loan Type
                </TableHead>
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {applications?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item?.personalInfo?.applicationCode}</TableCell>
                  <TableCell>{item?.personalInfo?.fullName}</TableCell>

                  <TableCell>{item?.personalInfo?.email}</TableCell>
                  <TableCell>
                    {dateFormat(item?.personalInfo?.dateOfBirth)}
                  </TableCell>
                  <TableCell>
                    {item?.loanInfo?.loanProductId?.loanTypeId?.name}
                  </TableCell>

                  <TableCell>
                    <div className="flex flex-row gap-1.5 items-center">
                      <DropdownMenu>
                        <DropdownMenuTrigger>
                          <Button variant="outline">
                            Select Loan Application{" "}
                            <ChevronDown className="ml-2 h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-[230px]">
                          {data?.map((loan, index) => (
                            <DropdownMenuItem
                              key={index}
                              onClick={() =>
                                handleSelect(loan?.loanTypeId?.name, item?._id)
                              }
                            >
                              {loan?.loanTypeId?.name}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          <CustomPagination
            currentPage={page}
            totalPages={totalPages}
            limit={limit}
            onPageChange={setPage}
            onLimitChange={setLimit}
          />
        </div>
      </div>
    </div>
  );
};

export default ApplicationList;
