import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import LoginImage from "../../assets/login.svg";

const formSchema = z.object({
  email: z.string().min(1).max(50),
  password: z.string().min(8).max(50),
});
const SignUp = () => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  function onSubmit(values) {
    console.log(values);
  }

  return (
    <div className="flex">
      <div className="w-1/2 border-r-2">
        <div className="flex justify-center items-center h-screen p-12 flex-col gap-6">
          <div className="flex flex-col justify-center gap-4 items-center">
            <h1 className="heading-text">Sign In</h1>
            <p className="secondary-text">Sign in to stay connected.</p>
          </div>

          <div className="w-full pr-20 pl-20">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-8"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-main-color">
                        Email <FormMessage />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter Email" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Password <FormMessage />
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter Password" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex items-center justify-center">
                  <Button type="submit">Submit</Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
      <div className="w-1/2">
        <div className="h-screen overflow-hidden">
          <img src={LoginImage} />
        </div>
      </div>
    </div>
  );
};

export default SignUp;
