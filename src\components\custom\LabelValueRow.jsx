/* eslint-disable react/prop-types */
import { cn } from "@/lib/utils";

const LabelValueRow = ({ label, value, className }) => {
  return (
    <div className={cn("space-y-2", className)}>
      <div className="text-sm text-muted-foreground">
        <span className="text-primary font-medium">{label} :</span>
        <span className="font-semibold"> {value}</span>
      </div>
    </div>
  );
};

export default LabelValueRow;
