Task to be completed.

1. Complete the loan application with all the loan application form word files
2. make the json for each form
3. should also able to add applicant in a way where when user click on the add applicant button it should call the api and in response it get the loanApplicationId from that id you can add new multiple applicants.
4. also make the design cleaner and more appealing in forms.
