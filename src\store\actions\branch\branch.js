import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_HOST } from "../../../constants";
import { getHeaders } from "../../header/requestHeader";
import {
  setBranch,
  setBranchError,
  setBranchLoading,
} from "../../reducer/branch/branch";
// import { getHeaders } from "../../utils/requestHeaders";

export const branchApi = createApi({
  reducerPath: "branchApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_HOST}`,
  }),

  endpoints: (builder) => ({
    getBranch: builder.mutation({
      query: ({ page = 1, limit = 10 }) => ({
        url: `/branch/get?page=${page}&limit=${limit}`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setBranchLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          dispatch(
            setBranch({
              data: response.data.data.data,
              currentPage: Number(response.data.data.currentPage),
              totalPages: Number(response.data.data.totalPages),
              totalBranches: Number(response.data.data.totalItems),
            })
          );
        } catch (e) {
          console.log(">>>>>setBranch error", e);
          dispatch(setBranchError(e.message || "Failed to load branches"));
        }
      },
    }),

    getSingleBranch: builder.mutation({
      query: (id) => ({
        url: `/branch/get/${id}`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setBranchLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          console.log(response.data.data);
          dispatch(
            setBranch({
              branch: response.data.data,
            })
          );
        } catch (e) {
          console.log(">>>>>setBranch error", e);
          dispatch(setBranchError(e.message || "Failed to load branches"));
        }
      },
    }),

    addBranch: builder.mutation({
      query: (userData) => ({
        url: `/branch/create`,
        method: "POST",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    deleteBranch: builder.mutation({
      query: (id) => ({
        url: `/branch/delete/${id}`,
        method: "DELETE",
        headers: getHeaders(),
      }),
    }),
  }),
});
// Export hooks for usage in functional components
export const {
  useAddBranchMutation,
  useDeleteBranchMutation,
  useGetBranchMutation,
  useGetSingleBranchMutation,
} = branchApi;
