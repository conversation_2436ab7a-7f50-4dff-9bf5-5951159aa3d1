import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_HOST } from "../../../constants";
import { getHeaders } from "../../header/requestHeader";
import {
  setPrimaryApplicationLoading,
  setPrimaryApplication,
  setPrimaryApplicationError,
} from "../../reducer/primaryApplication/primaryApplication";
import { transformLoanData } from "../../../lib/utils";
// import { getHeaders } from "../../utils/requestHeaders";

export const primaryApplicationApi = createApi({
  reducerPath: "primaryApplicationApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_HOST}`,
  }),

  endpoints: (builder) => ({
    addPersonalInfo: builder.mutation({
      query: (userData) => ({
        url: `/primary-application/create`,
        method: "POST",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    addLoanDetails: builder.mutation({
      query: (userData) => ({
        url: `/primary-application/loan-details`,
        method: "POST",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    addCollateralDetails: builder.mutation({
      query: (userData) => ({
        url: `/primary-application/collateral-details`,
        method: "POST",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    addDocumentDetails: builder.mutation({
      query: (userData) => ({
        url: `/primary-application/document-details`,
        method: "POST",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    updateApplicationStatus: builder.mutation({
      query: ({ primaryApplicationId, userData }) => ({
        url: `/primary-application/${primaryApplicationId}/status`,
        method: "PATCH",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    getPrimaryApplication: builder.mutation({
      query: (primaryApplicationId) => ({
        url: `/primary-application/${primaryApplicationId}`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setPrimaryApplicationLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          console.log("response >> ", response);
          dispatch(
            setPrimaryApplication({
              data: response.data.data,
            })
          );
        } catch (e) {
          console.log(">>>>>setPrimaryApplication error", e);
          dispatch(
            setPrimaryApplicationError(e.message || "Failed to load branches")
          );
        }
      },
    }),

    getListPrimaryApplication: builder.mutation({
      query: ({ page = 1, limit = 10, ...filters }) => {
        const params = new URLSearchParams({
          page,
          limit,
          ...filters,
        });

        return {
          url: `/primary-application?${params.toString()}`,
          method: "GET",
          headers: getHeaders(),
        };
      },

      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setPrimaryApplicationLoading()); // loading = true

        try {
          const response = await queryFulfilled;
          console.log("response >> ", response.data.data);
          dispatch(
            setPrimaryApplication({
              applications: response.data.data,
              currentPage: Number(response.data.pagination.currentPage),
              totalPages: Number(response.data.pagination.totalPages),
              totalApplications: Number(response.data.pagination.totalItems),
            })
          );
        } catch (e) {
          dispatch(
            setPrimaryApplicationError(
              e.message || "Failed to load applications"
            )
          );
        }
      },
    }),

    getLoanType: builder.mutation({
      query: () => ({
        url: `/primary-application/loan-type`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setPrimaryApplicationLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          dispatch(
            setPrimaryApplication({
              loanTypes: transformLoanData(response.data.data),
            })
          );
        } catch (e) {
          console.log(">>>>>setPrimaryApplication error", e);
          dispatch(
            setPrimaryApplicationError(e.message || "Failed to load branches")
          );
        }
      },
    }),
  }),
});
// Export hooks for usage in functional components
export const {
  useAddPersonalInfoMutation,
  useAddLoanDetailsMutation,
  useGetLoanTypeMutation,
  useAddCollateralDetailsMutation,
  useAddDocumentDetailsMutation,
  useGetPrimaryApplicationMutation,
  useUpdateApplicationStatusMutation,
  useGetListPrimaryApplicationMutation,
} = primaryApplicationApi;
