import { createSlice } from "@reduxjs/toolkit";

const location = createSlice({
  name: "location",
  initialState: {
    country: [],
    state: [],
    city: [],
    isLoading: false,
    error: null,
  },
  reducers: {
    setLocation(state, action) {
      Object.entries(action.payload).forEach(([key, value]) => {
        if (key in state) {
          state[key] = value;
        }
      });
      state.isLoading = false;
      state.error = null;
    },
    setLocationLoading(state) {
      state.isLoading = true;
      state.error = null;
    },
    setLocationError(state, action) {
      state.isLoading = false;
      state.error = action.payload;
      state.data = [];
    },
  },
});

export const { setLocation, setLocationError, setLocationLoading } =
  location.actions;
export default location.reducer;
