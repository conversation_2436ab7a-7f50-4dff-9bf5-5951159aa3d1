/* eslint-disable react/prop-types */
import { useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";

const AutoScrollArea = ({ ids = [], subtractHeight = 100, children }) => {
  const [height, setHeight] = useState(400); // Default height

  useEffect(() => {
    const updateHeight = () => {
      let totalSubtractHeight = subtractHeight; // Start with additional space

      // Subtract heights of all elements with given IDs
      ids.forEach((id) => {
        const element = document.getElementById(id);
        if (element) {
          totalSubtractHeight += element.offsetHeight + 80;
        }
      });

      // Calculate available height
      const newHeight = window.innerHeight - totalSubtractHeight;
      setHeight(newHeight > 0 ? newHeight : 0); // Ensure non-negative height
    };

    updateHeight(); // Initial calculation
    window.addEventListener("resize", updateHeight);

    return () => window.removeEventListener("resize", updateHeight);
  }, [ids, subtractHeight]);

  return (
    <ScrollArea className="pr-4" style={{ height: `${height}px` }}>
      {children}
    </ScrollArea>
  );
};

export default AutoScrollArea;
