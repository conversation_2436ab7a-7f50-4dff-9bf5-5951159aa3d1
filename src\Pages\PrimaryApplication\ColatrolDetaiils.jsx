/* eslint-disable react/prop-types */
import AutoScrollArea from "@/components/custom/AutoScrollArea";
import FormSelect from "@/components/custom/SelectInput";
import InputField from "../../components/custom/InputField";
import DateField from "../../components/custom/DateField";

const CollateralDetails = ({ control }) => {
  return (
    <AutoScrollArea ids={["page-header", "progressBar"]} subtractHeight={233}>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="propertyOwnerName"
              label="Property Owner Name"
              placeholder="Enter Property Owner Name"
              isRequired
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="propertyAddress"
              label="Property Address"
              placeholder="Enter Property Address"
              isRequired
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <FormSelect
              label="Property Type"
              name="propertyType"
              control={control}
              options={[
                { value: "personal", name: "Personal" },
                { value: "agriculture", name: "Agriculture" },
                { value: "other", name: "Other" },
              ]}
            />
          </div>

          <div className="flex-1">
            <InputField
              control={control}
              name="landArea"
              label="Land Area"
              placeholder="Enter Land Area"
              isRequired
            />
          </div>

          <div className="flex-1">
            <InputField
              control={control}
              name="constructionArea"
              label="Construction Area"
              placeholder="Enter Construction Area"
              isRequired
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="propertyValuation"
              label="Property Valuation"
              placeholder="Enter Property Valuation"
              isRequired
            />
          </div>
          <div className="flex-1">
            <DateField
              control={control}
              name="valuationDate"
              label="Valuation Date"
              placeholder="Enter Valuation Date"
              isRequired
            />
          </div>
        </div>
      </div>
    </AutoScrollArea>
  );
};

export default CollateralDetails;
