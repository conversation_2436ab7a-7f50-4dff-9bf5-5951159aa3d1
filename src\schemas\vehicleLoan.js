const vehicleLoanSchema = {
  loanCode: "VL",
  title: "Vehicle Loan Application",
  description: "Apply for a vehicle loan to purchase your dream car or commercial vehicle",
  subtitle: "Complete all sections accurately to ensure quick processing of your application",
  category: "Vehicle Finance",
  estimatedTime: "15-20 minutes",
  sections: [
    {
      sectionId: "applicant_details",
      sectionTitle: "Applicant Personal Information",
      description: "Personal details of the loan applicant",
      iconName: "User",
      iconColor: "text-blue-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "applicantName",
          label: "Full Name",
          type: "text",
          required: true,
          placeholder: "Enter your complete name",
          helpText: "Name as per official documents"
        },
        {
          name: "father<PERSON><PERSON>",
          label: "Father's Name",
          type: "text",
          required: true,
          placeholder: "Enter father's name"
        },
        {
          name: "dateOfBirth",
          label: "Date of Birth",
          type: "date",
          required: true,
          helpText: "Date of birth as per official documents"
        },
        {
          name: "age",
          label: "Age",
          type: "number",
          required: true,
          min: 18,
          max: 70,
          placeholder: "25"
        },
        {
          name: "gender",
          label: "Gender",
          type: "radio",
          options: [
            { value: "male", label: "Male" },
            { value: "female", label: "Female" },
            { value: "other", label: "Other" }
          ],
          required: true
        },
        {
          name: "maritalStatus",
          label: "Marital Status",
          type: "select",
          required: true,
          options: [
            { value: "single", label: "Single" },
            { value: "married", label: "Married" },
            { value: "divorced", label: "Divorced" },
            { value: "widowed", label: "Widowed" }
          ]
        },
        {
          name: "contactInfo",
          label: "Contact Information",
          type: "group",
          gridClass: "grid-cols-2",
          colSpan: 2,
          fields: [
            {
              name: "mobileNumber",
              label: "Mobile Number",
              type: "tel",
              required: true,
              placeholder: "9876543210"
            },
            {
              name: "email",
              label: "Email Address",
              type: "email",
              required: true,
              placeholder: "<EMAIL>"
            }
          ]
        },
        {
          name: "address",
          label: "Permanent Address",
          type: "textarea",
          required: true,
          colSpan: 2,
          placeholder: "Enter complete permanent address",
          rows: 3
        }
      ]
    },
    {
      sectionId: "vehicle_details",
      sectionTitle: "Vehicle Information",
      description: "Details about the vehicle you want to purchase",
      iconName: "Car",
      iconColor: "text-orange-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "vehicleType",
          label: "Vehicle Type",
          type: "select",
          required: true,
          options: [
            { value: "car", label: "Car" },
            { value: "bike", label: "Two Wheeler" },
            { value: "commercial", label: "Commercial Vehicle" },
            { value: "tractor", label: "Tractor" }
          ]
        },
        {
          name: "vehicleMake",
          label: "Vehicle Make/Brand",
          type: "text",
          required: true,
          placeholder: "e.g., Maruti, Honda, Tata"
        },
        {
          name: "vehicleModel",
          label: "Vehicle Model",
          type: "text",
          required: true,
          placeholder: "e.g., Swift, City, Nexon"
        },
        {
          name: "vehicleVariant",
          label: "Vehicle Variant",
          type: "text",
          placeholder: "e.g., VXI, ZX, XZ+"
        },
        {
          name: "manufacturingYear",
          label: "Manufacturing Year",
          type: "number",
          required: true,
          min: 2015,
          max: 2025,
          placeholder: "2024"
        },
        {
          name: "vehicleCondition",
          label: "Vehicle Condition",
          type: "radio",
          required: true,
          options: [
            { value: "new", label: "New" },
            { value: "used", label: "Used" }
          ]
        },
        {
          name: "vehiclePrice",
          label: "Vehicle Price (₹)",
          type: "number",
          required: true,
          placeholder: "500000",
          min: 50000,
          helpText: "Ex-showroom price or agreed purchase price"
        },
        {
          name: "dealerName",
          label: "Dealer/Seller Name",
          type: "text",
          required: true,
          placeholder: "Enter dealer or seller name"
        }
      ]
    },
    {
      sectionId: "loan_details",
      sectionTitle: "Loan Requirements",
      description: "Specify the loan amount and repayment terms",
      iconName: "DollarSign",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "loanAmount",
          label: "Loan Amount Required (₹)",
          type: "number",
          required: true,
          placeholder: "400000",
          min: 50000,
          max: 5000000,
          helpText: "Amount you want to borrow"
        },
        {
          name: "downPayment",
          label: "Down Payment (₹)",
          type: "number",
          required: true,
          placeholder: "100000",
          helpText: "Amount you will pay upfront"
        },
        {
          name: "loanTenure",
          label: "Loan Tenure (Years)",
          type: "select",
          required: true,
          options: [
            { value: "1", label: "1 Year" },
            { value: "2", label: "2 Years" },
            { value: "3", label: "3 Years" },
            { value: "4", label: "4 Years" },
            { value: "5", label: "5 Years" },
            { value: "6", label: "6 Years" },
            { value: "7", label: "7 Years" }
          ]
        },
        {
          name: "interestType",
          label: "Interest Type",
          type: "radio",
          required: true,
          options: [
            { value: "fixed", label: "Fixed Rate" },
            { value: "floating", label: "Floating Rate" }
          ]
        }
      ]
    },
    {
      sectionId: "income_details",
      sectionTitle: "Income Information",
      description: "Details about your income and employment",
      iconName: "Briefcase",
      iconColor: "text-purple-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "employmentType",
          label: "Employment Type",
          type: "select",
          required: true,
          options: [
            { value: "salaried", label: "Salaried" },
            { value: "self_employed", label: "Self Employed" },
            { value: "business", label: "Business Owner" },
            { value: "professional", label: "Professional" }
          ]
        },
        {
          name: "monthlyIncome",
          label: "Monthly Income (₹)",
          type: "number",
          required: true,
          placeholder: "50000",
          min: 15000,
          helpText: "Gross monthly income"
        },
        {
          name: "companyName",
          label: "Company/Business Name",
          type: "text",
          required: true,
          placeholder: "Enter company or business name"
        },
        {
          name: "workExperience",
          label: "Work Experience (Years)",
          type: "number",
          required: true,
          min: 0,
          max: 50,
          placeholder: "5"
        },
        {
          name: "officeAddress",
          label: "Office/Business Address",
          type: "textarea",
          required: true,
          colSpan: 2,
          placeholder: "Enter complete office or business address",
          rows: 3
        }
      ]
    },
    {
      sectionId: "financial_details",
      sectionTitle: "Financial Information",
      description: "Additional financial details and existing obligations",
      iconName: "Calculator",
      iconColor: "text-indigo-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "existingLoans",
          label: "Existing Loan EMIs (₹)",
          type: "number",
          placeholder: "0",
          helpText: "Total monthly EMI of existing loans"
        },
        {
          name: "bankAccount",
          label: "Primary Bank Account",
          type: "text",
          required: true,
          placeholder: "Enter bank name and account details"
        },
        {
          name: "panNumber",
          label: "PAN Number",
          type: "text",
          required: true,
          placeholder: "**********",
          pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}",
          helpText: "Permanent Account Number"
        },
        {
          name: "aadharNumber",
          label: "Aadhar Number",
          type: "text",
          required: true,
          placeholder: "1234 5678 9012",
          pattern: "[0-9]{4}\\s[0-9]{4}\\s[0-9]{4}",
          helpText: "12-digit Aadhar number"
        }
      ]
    },
    {
      sectionId: "declaration",
      sectionTitle: "Declaration",
      description: "Legal declarations and commitments",
      iconName: "CheckCircle",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "informationAccuracy",
          label: "I/We declare that all information provided is true and accurate",
          type: "checkbox",
          required: true
        },
        {
          name: "termsAcceptance",
          label: "I/We accept the terms and conditions of the vehicle loan",
          type: "checkbox",
          required: true
        },
        {
          name: "repaymentCommitment",
          label: "I/We commit to repay the loan as per the agreed schedule",
          type: "checkbox",
          required: true
        },
        {
          name: "documentVerification",
          label: "I/We authorize the bank to verify all submitted documents",
          type: "checkbox",
          required: true
        }
      ]
    }
  ]
};

export default vehicleLoanSchema;
