import { createSlice } from "@reduxjs/toolkit";

const branch = createSlice({
  name: "branch",
  initialState: {
    data: [],
    branch: {},
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalBranches: 0,
  },
  reducers: {
    setBranch(state, action) {
      Object.entries(action.payload).forEach(([key, value]) => {
        if (key in state) {
          // Type assertion to satisfy TypeScript
          state[key] = value;
        }
      });
      state.isLoading = false;
      state.error = null;
    },
    setBranchLoading(state) {
      state.isLoading = true;
      state.error = null;
    },
    setBranchError(state, action) {
      state.isLoading = false;
      state.error = action.payload;
      state.data = [];
    },
  },
});

export const { setBranch, setBranchLoading, setBranchError } = branch.actions;
export default branch.reducer;
