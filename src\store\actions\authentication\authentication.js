import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_HOST } from "../../../constants";
import { getHeaders } from "../../header/requestHeader";
// import { getHeaders } from "../../utils/requestHeaders";

export const authenticationApi = createApi({
  reducerPath: "authenticationApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_HOST}`,
  }),

  endpoints: (builder) => ({
    loginUser: builder.mutation({
      query: (userData) => ({
        url: `/auth/login`,
        method: "POST",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    logoutUser: builder.mutation({
      query: () => ({
        url: `/logout`,
        method: "GET",
        headers: getHeaders(),
      }),
    }),
  }),
});
// Export hooks for usage in functional components
export const { useLoginUserMutation, useLogoutUserMutation } =
  authenticationApi;
