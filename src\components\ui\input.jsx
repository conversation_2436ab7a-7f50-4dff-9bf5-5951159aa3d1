import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, error, ...props }) {
  let exceptThisSymbols = [];
  if (type === "number") {
    exceptThisSymbols = ["e", "E", "+", "-"];
  }

  const handlePaste = (e) => {
    const paste = (e.clipboardData || window.clipboardData).getData("text");
    if (exceptThisSymbols.some((symbol) => paste.includes(symbol))) {
      e.preventDefault();
    }
  };

  return (
    <input
      type={type}
      data-slot="input"
      onWheel={(e) => {
        if (e.currentTarget.type === "number") e.currentTarget.blur();
      }}
      onPaste={type === "number" ? handlePaste : null}
      onKeyDown={(e) => exceptThisSymbols.includes(e.key) && e.preventDefault()}
      className={cn(
        "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors aria-invalid:ring-destructive/20 aria-invalid:border-destructive file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-secondary-text focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ",

        className
      )}
      {...props}
    />
  );
}

export { Input };
