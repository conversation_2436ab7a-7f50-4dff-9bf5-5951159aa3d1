/* eslint-disable react/prop-types */
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const RadioButton = ({
  name,
  label,
  options,
  formControl,
  direction = "row",
  isRequired = false,
}) => {
  return (
    <FormField
      control={formControl}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            {label} {isRequired ? <span className="text-red-500">*</span> : ""}
          </FormLabel>
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              defaultValue={field.value}
              className={`flex flex-${direction} space-${
                direction === "row" ? "x" : "y"
              }-2`}
            >
              {options.map((option, index) => (
                <FormItem
                  key={option.value}
                  className="flex items-center space-x-1 space-y-0"
                >
                  <FormControl>
                    <RadioGroupItem
                      value={option.value}
                      name={field.name} // ✅ Needed for querySelector `[name]`
                      ref={index === 0 ? field.ref : undefined} // ✅ Only first one gets ref
                    />
                  </FormControl>
                  <FormLabel
                    className={`font-normal ${
                      field.value === option.value
                        ? "text-primary-color font-medium"
                        : "text-muted-foreground"
                    }`}
                  >
                    {option.label}
                  </FormLabel>
                </FormItem>
              ))}
            </RadioGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default RadioButton;
