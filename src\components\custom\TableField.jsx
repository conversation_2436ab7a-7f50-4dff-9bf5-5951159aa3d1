/* eslint-disable react/prop-types */
import { useFieldArray, useFormContext } from "react-hook-form";
import InputField from "@/components/custom/InputField";
import { useEffect } from "react";

export default function TableField({ name, label, columns, rows = 3 }) {
  const { control } = useFormContext();

  const { fields, append } = useFieldArray({
    control,
    name,
  });

  // 👇 Append empty rows safely on initial render
  useEffect(() => {
    if (fields.length < rows) {
      const missing = rows - fields.length;
      for (let i = 0; i < missing; i++) {
        append({});
      }
    }
  }, [rows, fields.length, append]);

  return (
    <div className="mb-4">
      <label className="block font-medium mb-2">{label}</label>
      <div className="overflow-x-auto">
        <table className="min-w-full text-sm border rounded">
          <thead>
            <tr className="bg-gray-100 text-left">
              {columns.map((col) => (
                <th key={col.name} className="p-2 border">
                  {col.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {fields.map((_, rowIndex) => (
              <tr key={rowIndex}>
                {columns.map((col) => (
                  <td key={col.name} className="p-2 border">
                    <InputField
                      name={`${name}[${rowIndex}].${col.name}`}
                      control={control}
                      type={col.type || "text"}
                      placeholder={col.label}
                    />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
