/* eslint-disable react/prop-types */
import { ChevronsUpDown, LogOut } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { NavLink, useNavigate } from "react-router-dom";
import { useLogoutUserMutation } from "../../store/actions";
import { setApiError } from "../../hooks/errorMessage";
export function NavUser({ user }) {
  const [logoutApi] = useLogoutUserMutation();
  const navigate = useNavigate();
  const { isMobile } = useSidebar();

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="relative">
              <SidebarMenuButton
                size="lg"
                className="w-full px-4 py-3 hover:bg-gray-50 transition-colors duration-200 rounded-xl border border-transparent hover:border-gray-200/50"
              >
                <Avatar className="h-10 w-10 rounded-full border-2 border-gradient-to-br from-sidebar-active-color/20 to-primary-color/10 shadow-sm">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="bg-gradient-to-br from-sidebar-active-color to-primary-color text-white font-semibold text-sm">
                    {user.name.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight ml-3">
                  <span className="truncate font-semibold text-gray-800">
                    {user.name}
                  </span>
                  <span className="truncate text-xs text-gray-500 font-medium">
                    {user.email}
                  </span>
                </div>
                <ChevronsUpDown className="h-4 w-4 text-gray-400" />
              </SidebarMenuButton>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-64 bg-white/95 backdrop-blur-sm border border-gray-200/50 shadow-xl rounded-xl p-2"
          >
            <DropdownMenuLabel className="text-gray-800 font-semibold px-3 py-2">
              My Account
            </DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-gray-200/50" />
            <DropdownMenuItem className="px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50 transition-all duration-200 cursor-pointer">
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem className="px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50 transition-all duration-200 cursor-pointer">
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-200/50" />
            <DropdownMenuItem
              className="text-red-500 focus:text-red-500 px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100/50 transition-all duration-200 cursor-pointer"
              onClick={() => {
                // Your existing logout logic
              }}
            >
              <LogOut className="mr-2 h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
