import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  useGetCountriesMutation,
  useGetCityMutation,
  useGetStateMutation,
} from "../store/actions";
import { mapToLabelValueArray } from "../lib/utils";

/**
 * Custom hook to manage location dropdowns (Country, State, City)
 * @param {Object} fieldKeys - An object with keys:
 * @param {string} fieldKeys.country - form field key for country
 * @param {string} fieldKeys.state - form field key for state
 * @param {string} fieldKeys.city - form field key for city
 */
export const useLocationDropdowns = ({ country, state, city }) => {
  const { setValue, watch } = useFormContext();

  const [getCountries] = useGetCountriesMutation();
  const [getStates] = useGetStateMutation();
  const [getCities] = useGetCityMutation();

  const [countriesOptions, setCountriesOptions] = useState([]);
  const [statesOptions, setStatesOptions] = useState([]);
  const [citiesOptions, setCitiesOptions] = useState([]);

  const selectedCountry = watch(country);
  const selectedState = watch(state);

  // Fetch Countries
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const res = await getCountries().unwrap();
        setCountriesOptions(mapToLabelValueArray(res.data, "name", "code"));
      } catch (error) {
        console.error("Error fetching countries:", error);
      }
    };
    fetchCountries();
  }, [getCountries]);

  // Fetch States when country changes
  useEffect(() => {
    const fetchStates = async () => {
      if (!selectedCountry) return;

      try {
        const res = await getStates(selectedCountry).unwrap();
        setStatesOptions(mapToLabelValueArray(res.data, "name", "_id"));

        // Preserve state and city values if already set
        if (!watch(state)) setValue(state, null);
        if (!watch(city)) {
          setValue(city, null);
          setCitiesOptions([]);
        } else {
          // Fetch cities if state is already selected
          fetchCities(watch(state));
        }
      } catch (error) {
        console.error("Error fetching states:", error);
      }
    };

    fetchStates();
  }, [selectedCountry, getStates, watch, setValue]);

  // Fetch Cities when state changes
  useEffect(() => {
    if (selectedState) {
      fetchCities(selectedState);
    }
  }, [selectedState]);

  const fetchCities = async (stateId) => {
    try {
      const res = await getCities(stateId).unwrap();
      setCitiesOptions(mapToLabelValueArray(res.data, "name", "_id"));

      // Preserve city value if already set
      if (!watch(city)) setValue(city, null);
    } catch (error) {
      console.error("Error fetching cities:", error);
    }
  };

  return {
    countriesOptions,
    statesOptions,
    citiesOptions,
  };
};

export default useLocationDropdowns;
