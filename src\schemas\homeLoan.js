const housingLoanSchema = {
  loanCode: "HL",
  title: "Home Loan Application",
  description: "Apply for a home loan to purchase, construct, or renovate your dream home",
  subtitle: "Complete all sections accurately to ensure quick processing of your application",
  category: "Housing Finance",
  estimatedTime: "15-20 minutes",
  sections: [
    {
      sectionId: "applicant_selection",
      sectionTitle: "Select Primary Applicant",
      description: "Choose the primary applicant for this loan application",
      iconName: "UserCheck",
      iconColor: "text-blue-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "selectedPrimaryApplicantId",
          label: "Select Primary Applicant",
          type: "select",
          onlyForAdditionalApplicant: true,
          required: true,
          dynamicOptionsSource: "primaryApplications",
          colSpan: 2,
          placeholder: "Choose from existing applications",
          helpText: "Select the primary applicant from your existing applications"
        },
      ],
    },
    {
      sectionId: "application_info",
      sectionTitle: "Application Information",
      description: "Basic information about your loan application",
      iconName: "FileText",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "applicationNumber",
          label: "Application Number",
          type: "text",
          required: true,
          colSpan: 1,
          placeholder: "Auto-generated",
          helpText: "Unique identifier for your application",
          disabled: true
        },
        {
          name: "applicationDate",
          label: "Application Date",
          type: "date",
          required: true,
          colSpan: 1,
          helpText: "Date when you're submitting this application"
        },
        {
          name: "riskCategory",
          label: "Risk Assessment Category",
          type: "radio",
          options: [
            { value: "low", label: "Low Risk", description: "Stable income, good credit history" },
            { value: "medium", label: "Medium Risk", description: "Average income, fair credit history" },
            { value: "high", label: "High Risk", description: "Variable income, limited credit history" }
          ],
          required: true,
          colSpan: 2,
          helpText: "Risk category will be determined based on your financial profile"
        },
      ]
    },
    {
      sectionId: "primary_applicant",
      sectionTitle: "Primary Applicant Details",
      description: "Personal information of the main loan applicant",
      iconName: "User",
      iconColor: "text-purple-600",
      required: true,
      gridClass: "grid-cols-1 lg:grid-cols-2 gap-6",
      fields: [
        {
          name: "fullName",
          label: "Full Name",
          type: "group",
          gridClass: "grid-cols-1 md:grid-cols-3 gap-4",
          colSpan: "full",
          lockedForPrimary: true,
          helpText: "Enter your complete legal name as per official documents",
          fields: [
            {
              name: "firstName",
              label: "First Name",
              type: "text",
              required: true,
              placeholder: "Enter first name"
            },
            {
              name: "middleName",
              label: "Middle Name",
              type: "text",
              placeholder: "Enter middle name (optional)"
            },
            {
              name: "lastName",
              label: "Last Name",
              type: "text",
              required: true,
              placeholder: "Enter last name"
            }
          ]
        },
        {
          name: "customerNumber",
          label: "Customer Number (UCIC)",
          type: "text",
          required: true,
          placeholder: "Enter UCIC number",
          helpText: "Unique Customer Identification Code from your bank"
        },
        {
          name: "ckycrNumber",
          label: "CKYCR Number",
          type: "text",
          placeholder: "Enter CKYCR number",
          helpText: "Central KYC Registry Number (if available)"
        },
        {
          name: "gender",
          label: "Gender",
          type: "radio",
          options: [
            { value: "male", label: "Male" },
            { value: "female", label: "Female" },
            { value: "other", label: "Other" }
          ],
          required: true,
          helpText: "Select your gender as per official documents"
        },
        {
          name: "age",
          label: "Age",
          type: "number",
          required: true,
          placeholder: "Enter age",
          min: 18,
          max: 70,
          helpText: "Age should be between 18 and 70 years"
        },
        {
          name: "contactInfo",
          label: "Contact Information",
          type: "group",
          gridClass: "grid-cols-1 md:grid-cols-3 gap-4",
          colSpan: "full",
          helpText: "Provide your current contact details for communication",
          fields: [
            {
              name: "phoneCode",
              label: "Country Code",
              type: "select",
              required: true,
              colSpan: 1,
              options: [
                { label: "+91 (India)", value: "+91" },
                { label: "+1 (USA)", value: "+1" },
                { label: "+44 (UK)", value: "+44" },
              ],
            },
            {
              name: "phone",
              label: "Phone Number",
              type: "tel",
              required: true,
              colSpan: 1,
              placeholder: "9876543210",
              pattern: "[0-9]{10}",
              helpText: "10-digit mobile number"
            },
            {
              name: "email",
              label: "Email Address",
              type: "email",
              required: true,
              colSpan: 1,
              placeholder: "<EMAIL>",
              helpText: "Valid email address for notifications"
            }
          ]
        },
        {
          name: "identification",
          label: "Identification Documents",
          type: "group",
          gridClass: "grid-cols-1 md:grid-cols-2 gap-4",
          colSpan: 2,
          helpText: "Government issued identification documents",
          fields: [
            {
              name: "pan",
              label: "PAN Number",
              type: "text",
              required: true,
              placeholder: "**********",
              pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}",
              helpText: "10-character PAN number"
            },
            {
              name: "aadhaar",
              label: "Aadhaar Number",
              type: "text",
              required: true,
              placeholder: "1234 5678 9012",
              pattern: "[0-9]{4}\\s[0-9]{4}\\s[0-9]{4}",
              helpText: "12-digit Aadhaar number"
            }
          ]
        },
        {
          name: "address",
          label: "Residential Address",
          type: "textarea",
          required: true,
          colSpan: "full",
          placeholder: "Enter your complete residential address",
          helpText: "Current residential address as per address proof",
          rows: 3
        },
        {
          name: "nationality",
          label: "Nationality",
          type: "radio",
          options: [
            { value: "indian", label: "Indian Citizen" },
            { value: "nri", label: "Non-Resident Indian (NRI)" },
            { value: "other", label: "Other" }
          ],
          required: true,
          helpText: "Select your nationality status"
        },
      ]
    },
    {
      sectionId: "existing_loans",
      sectionTitle: "Existing Loan Facilities",
      description: "Information about your current loans and financial obligations",
      iconName: "CreditCard",
      iconColor: "text-orange-600",
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "hasExistingLoans",
          label: "Do you have any existing loans or credit facilities?",
          type: "radio",
          options: [
            { value: "yes", label: "Yes, I have existing loans" },
            { value: "no", label: "No, I don't have any existing loans" }
          ],
          required: true,
          helpText: "Include all types of loans - personal, vehicle, credit cards, etc."
        },
        {
          name: "existingLoans",
          label: "Existing Loan Details",
          type: "table",
          dependsOn: { name: "hasExistingLoans", value: "yes" },
          helpText: "Provide details of all your existing loans and credit facilities",
          columns: [
            { name: "bank", label: "Bank/Financial Institution", placeholder: "Enter bank name and branch" },
            { name: "loanType", label: "Loan Type", placeholder: "Personal/Vehicle/Home/etc." },
            { name: "limit", label: "Sanctioned Limit (₹)", type: "number", placeholder: "0" },
            { name: "outstanding", label: "Outstanding Amount (₹)", type: "number", placeholder: "0" },
            { name: "collateral", label: "Collateral/Security", placeholder: "Property/Vehicle/None" },
            { name: "remarks", label: "Additional Remarks", placeholder: "Any additional information" }
          ],
          rows: 3,
          addRowText: "Add Another Loan",
          removeRowText: "Remove Loan"
        }
      ]
    },
    {
      sectionId: "guarantor_details",
      sectionTitle: "Guarantor Details",
      description: "Information about guarantors who will co-sign your loan",
      iconName: "Shield",
      iconColor: "text-indigo-600",
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "guarantorRequired",
          label: "Do you have guarantors for this loan?",
          type: "radio",
          options: [
            { value: "yes", label: "Yes, I have guarantors" },
            { value: "no", label: "No, I don't have guarantors" }
          ],
          required: true,
          colSpan: 2,
          helpText: "Guarantors provide additional security for your loan application"
        },
        {
          name: "guarantor1",
          label: "Guarantor 1 Details",
          type: "group",
          colSpan: 2, // Full width group
          dependsOn: { name: "guarantorRequired", value: "yes" },
          fields: [
            {
              name: "fullName",
              label: "Full Name",
              type: "text",
              required: true,
              colSpan: 1
            },
            {
              name: "age",
              label: "Age",
              type: "number",
              colSpan: 1
            },
            {
              name: "memberNumber",
              label: "Member Number",
              type: "text",
              colSpan: 1
            },
            {
              name: "contactInfo",
              label: "Contact Information",
              type: "text",
              colSpan: 1
            },
            {
              name: "businessAddress",
              label: "Business Address",
              type: "textarea",
              colSpan: 2

            },
            {
              name: "capitalInvested",
              label: "Capital Invested",
              type: "number",
              colSpan: 1

            },
            {
              name: "monthlyIncome",
              label: "Monthly/Annual Income",
              type: "number",
              colSpan: 1
            },
            {
              name: "propertyDetails",
              label: "Property Ownership Details",
              type: "textarea",
              colSpan: 1
            },
            {
              name: "estimatedValue",
              label: "Estimated Value",
              type: "number",
              colSpan: 1

            },
            {
              name: "movableAssets",
              label: "Movable Assets",
              type: "textarea",
              colSpan: 1

            },
            {
              name: "investments",
              label: "Investments",
              type: "textarea",
              colSpan: 1

            },
            {
              name: "netWorth",
              label: "Net Worth",
              type: "number",
              colSpan: 1

            },
            {
              name: "existingMortgages",
              label: "Existing Mortgages",
              type: "textarea",
              colSpan: 1

            }
          ]
        },
        {
          name: "guarantor2",
          label: "Guarantor 2 Details",
          type: "group",
          dependsOn: { name: "guarantorRequired", value: "yes" },
          fields: [
            {
              name: "fullName",
              label: "Full Name",
              type: "text",
              required: true,
              colSpan: 1
            },
            {
              name: "age",
              label: "Age",
              type: "number",
              colSpan: 1
            },
            {
              name: "memberNumber",
              label: "Member Number",
              type: "text",
              colSpan: 1
            },
            {
              name: "contactInfo",
              label: "Contact Information",
              type: "text",
              colSpan: 1
            },
            {
              name: "businessAddress",
              label: "Business Address",
              type: "textarea",
              colSpan: 2

            },
            {
              name: "capitalInvested",
              label: "Capital Invested",
              type: "number",
              colSpan: 1

            },
            {
              name: "monthlyIncome",
              label: "Monthly/Annual Income",
              type: "number",
              colSpan: 1
            },
            {
              name: "propertyDetails",
              label: "Property Ownership Details",
              type: "textarea",
              colSpan: 1
            },
            {
              name: "estimatedValue",
              label: "Estimated Value",
              type: "number",
              colSpan: 1

            },
            {
              name: "movableAssets",
              label: "Movable Assets",
              type: "textarea",
              colSpan: 1

            },
            {
              name: "investments",
              label: "Investments",
              type: "textarea",
              colSpan: 1

            },
            {
              name: "netWorth",
              label: "Net Worth",
              type: "number",
              colSpan: 1

            },
            {
              name: "existingMortgages",
              label: "Existing Mortgages",
              type: "textarea",
              colSpan: 1

            }
          ]
        }
      ]
    },
    {
      sectionId: "loan_request",
      sectionTitle: "Loan Request Details",
      description: "Specify the loan amount, purpose, and repayment terms",
      iconName: "DollarSign",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "loanPurpose",
          label: "Purpose of Home Loan",
          type: "radio",
          options: [
            {
              value: "construction",
              label: "Building Construction",
              description: "For constructing a new house on owned land"
            },
            {
              value: "purchase",
              label: "Purchase Ready House/Flat",
              description: "For buying an existing residential property"
            },
            {
              value: "renovation",
              label: "Renovation/Extension",
              description: "For renovating or extending existing property"
            }
          ],
          required: true,
          colSpan: 2,
          helpText: "Select the primary purpose for which you need the home loan"
        },
        {
          name: "loanAmount",
          label: "Requested Loan Amount (₹)",
          type: "number",
          required: true,
          placeholder: "5000000",
          min: 100000,
          max: 50000000,
          helpText: "Amount should be between ₹1 lakh and ₹5 crores"
        },
        {
          name: "loanTerm",
          label: "Loan Term (Years)",
          type: "number",
          required: true,
          placeholder: "20",
          min: 5,
          max: 30,
          helpText: "Loan tenure between 5 to 30 years"
        },
        {
          name: "moratoriumPeriod",
          label: "Moratorium Period (Months)",
          type: "number",
          placeholder: "6",
          min: 0,
          max: 24,
          helpText: "Grace period before EMI starts (optional, max 24 months)"
        },
        {
          name: "monthlyIncome",
          label: "Monthly Household Income (₹)",
          type: "number",
          required: true,
          placeholder: "100000",
          helpText: "Combined monthly income of all earning members"
        }
      ]
    },
    {
      sectionId: "property_details",
      sectionTitle: "Property Details",
      description: "Complete information about the property for which loan is required",
      iconName: "Home",
      iconColor: "text-blue-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "propertyAddress",
          label: "Complete Property Address",
          type: "textarea",
          required: true,
          colSpan: 2,
          placeholder: "Enter complete address with pincode",
          helpText: "Provide the complete address of the property including city, state, and pincode",
          rows: 3
        },
        {
          name: "surveyNumber",
          label: "Survey/Plot Number",
          type: "text",
          placeholder: "Enter survey or plot number",
          helpText: "Official survey number or plot number from revenue records"
        },
        {
          name: "propertyType",
          label: "Property Type",
          type: "select",
          required: true,
          options: [
            { value: "apartment", label: "Apartment/Flat" },
            { value: "independent_house", label: "Independent House" },
            { value: "villa", label: "Villa" },
            { value: "plot", label: "Plot/Land" },
            { value: "row_house", label: "Row House" }
          ],
          helpText: "Select the type of property"
        },
        {
          name: "propertyMeasurements",
          label: "Property Measurements",
          type: "group",
          gridClass: "grid-cols-1 md:grid-cols-2 gap-4",
          colSpan: "full",
          helpText: "Provide accurate measurements as per approved plans",
          fields: [
            {
              name: "landArea",
              label: "Land Area (sq ft)",
              type: "number",
              placeholder: "1200",
              min: 100
            },
            {
              name: "constructionArea",
              label: "Built-up Area (sq ft)",
              type: "number",
              placeholder: "1000",
              min: 100
            }
          ]
        },
        {
          name: "constructionCost",
          label: "Estimated Construction Cost (₹)",
          type: "number",
          dependsOn: { name: "loanPurpose", value: "construction" },
          placeholder: "2500000",
          helpText: "Total estimated cost for construction including materials and labor"
        },
        {
          name: "purchasePrice",
          label: "Property Purchase Price (₹)",
          type: "number",
          dependsOn: { name: "loanPurpose", value: "purchase" },
          placeholder: "5000000",
          helpText: "Agreed purchase price as per sale agreement"
        },
        {
          name: "renovationCost",
          label: "Renovation Cost (₹)",
          type: "number",
          dependsOn: { name: "loanPurpose", value: "renovation" }
        },
        {
          name: "ownershipDetails",
          label: "Ownership Details",
          type: "textarea",
          required: true
        },
        {
          name: "titleClearance",
          label: "Title Clearance Certificate Obtained?",
          type: "radio",
          options: [
            { value: "yes", label: "Yes" },
            { value: "no", label: "No" }
          ],
          required: true
        },
        {
          name: "approvedPlan",
          label: "Approved Construction Plan Available?",
          type: "radio",
          options: [
            { value: "yes", label: "Yes" },
            { value: "no", label: "No" }
          ],
          dependsOn: { name: "loanPurpose", value: "construction" }
        },
        {
          name: "commencementCertificate",
          label: "Construction Commencement Certificate",
          type: "radio",
          options: [
            { value: "yes", label: "Yes" },
            { value: "no", label: "No" }
          ],
          dependsOn: { name: "loanPurpose", value: "construction" }
        },
        {
          name: "completionCertificate",
          label: "Completion Certificate",
          type: "radio",
          options: [
            { value: "yes", label: "Yes" },
            { value: "no", label: "No" }
          ],
          dependsOn: { name: "loanPurpose", value: "purchase" }
        }
      ]
    },
    {
      sectionId: "declaration",
      sectionTitle: "Declaration and Undertaking",
      description: "Legal declarations and commitments required for loan processing",
      iconName: "CheckCircle",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "declaration",
          label: "I/We hereby declare and undertake the following:",
          type: "checkbox",
          required: true,
          colSpan: "full",
          helpText: "By checking this box, you agree to all the terms and declarations listed below",
          description: `
            • I/We declare that all the information provided in this application is true, complete, and correct to the best of my/our knowledge.

            • I/We undertake to inform the bank immediately about any material changes in the provided information, including changes in income, employment, or financial status.

            • I/We have read, understood, and accept the bank's terms and conditions for home loans. Terms and conditions are available on the bank's website.

            • I/We confirm that there are no pending legal proceedings against me/us or any criminal cases, including civil, criminal, or any other legal matters.

            • I/We agree to pay all applicable processing fees, charges, and penalties as per bank's schedule, including processing fee, valuation charges, legal charges, etc.

            • I/We undertake to use the property exclusively for residential purposes and not for commercial activities. Commercial use may violate loan terms and conditions.

            • I/We declare that this will be my/our primary residence and I/we do not own any other residential property (required for first-time home buyer benefits).

            This declaration is legally binding and any false information may result in rejection of the application or legal action.
          `
        }
      ]
    },
    {
      sectionId: "bank_use",
      sectionTitle: "For Bank Use Only",
      description: "Internal processing and approval details (to be filled by bank officials)",
      iconName: "Briefcase",
      iconColor: "text-indigo-600",
      gridClass: "grid-cols-1 gap-8",
      adminOnly: true,
      fields: [
        {
          name: "inspectionDetails",
          label: "Property Inspection Details",
          type: "group",
          gridClass: "grid-cols-1 md:grid-cols-3 gap-4",
          colSpan: "full",
          helpText: "Complete property inspection information",
          fields: [
            {
              name: "inspectingOfficer",
              label: "Inspecting Officer Name",
              type: "text",
              placeholder: "Enter officer name",
              colSpan: 1
            },
            {
              name: "inspectionDate",
              label: "Inspection Date",
              type: "date",
              colSpan: 1
            },
            {
              name: "remarks",
              label: "Inspection Remarks",
              type: "textarea",
              placeholder: "Enter detailed inspection remarks",
              colSpan: 1,
              rows: 3
            }
          ]
        },
        {
          name: "loanApprovalDetails",
          label: "Loan Approval Details",
          type: "group",
          gridClass: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
          colSpan: "full",
          helpText: "Financial approval and terms information",
          fields: [
            {
              name: "eligibleAmount",
              label: "Eligible Loan Amount (₹)",
              type: "number",
              placeholder: "0",
              min: 0,
              colSpan: 1
            },
            {
              name: "approvedAmount",
              label: "Approved Loan Amount (₹)",
              type: "number",
              placeholder: "0",
              min: 0,
              colSpan: 1
            },
            {
              name: "interestRate",
              label: "Interest Rate (%)",
              type: "number",
              placeholder: "0.00",
              min: 0,
              max: 100,
              step: 0.01,
              colSpan: 1
            },
            {
              name: "finalTerm",
              label: "Loan Term (Months)",
              type: "number",
              placeholder: "240",
              min: 12,
              max: 360,
              colSpan: 1
            },
            {
              name: "installmentAmount",
              label: "Monthly Installment (₹)",
              type: "number",
              placeholder: "0",
              min: 0,
              colSpan: 1
            },
            {
              name: "margin",
              label: "Margin (%)",
              type: "number",
              placeholder: "0.00",
              min: 0,
              max: 100,
              step: 0.01,
              colSpan: 1
            }
          ]
        },
        {
          name: "securityDetails",
          label: "Security & Collateral Details",
          type: "textarea",
          placeholder: "Enter detailed security and collateral information",
          colSpan: "full",
          rows: 4,
          helpText: "Describe the security arrangements and collateral details"
        },
        {
          name: "documentsRequired",
          label: "Required Documents Checklist",
          type: "group",
          gridClass: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3",
          colSpan: "full",
          helpText: "Select all documents required for loan processing",
          fields: [
            {
              name: "dpNote",
              label: "DP Note",
              type: "checkbox"
            },
            {
              name: "consentLetter",
              label: "Consent Letter",
              type: "checkbox"
            },
            {
              name: "installmentLetter",
              label: "Installment Letter",
              type: "checkbox"
            },
            {
              name: "loanAcknowledgement",
              label: "Loan Acknowledgement",
              type: "checkbox"
            },
            {
              name: "guaranteeLetter",
              label: "Guarantee Letter",
              type: "checkbox"
            },
            {
              name: "mortgage",
              label: "Equitable Mortgage",
              type: "checkbox"
            },
            {
              name: "undertaking",
              label: "Letter of Undertaking",
              type: "checkbox"
            },
            {
              name: "lien",
              label: "Letter of Lien and Set-off",
              type: "checkbox"
            }
          ]
        },
        {
          name: "approvalRecommendation",
          label: "Final Approval Recommendation",
          type: "textarea",
          placeholder: "Enter detailed approval recommendation with justification",
          colSpan: "full",
          rows: 5,
          helpText: "Provide comprehensive recommendation for loan approval/rejection with detailed reasoning"
        },
        {
          name: "approvingOfficer",
          label: "Approving Officer",
          type: "text"
        },
        {
          name: "approvalDate",
          label: "Approval Date",
          type: "date"
        }
      ]
    }
  ]
};

export default housingLoanSchema;