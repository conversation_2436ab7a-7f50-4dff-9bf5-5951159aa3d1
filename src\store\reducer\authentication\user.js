import { createSlice } from "@reduxjs/toolkit";

const user = createSlice({
    name: "user",
    initialState: {
        data: [],
        user: {},
        isLoading: false,
        error: null,
        currentPage: 1,
        totalPages: 1,
        totalUsers: 0,
    },
    reducers: {
        setUser(state, action) {
            Object.entries(action.payload).forEach(([key, value]) => {
                if (key in state) {
                    // Type assertion to satisfy TypeScript
                    state[key] = value;
                }
            });
            state.isLoading = false;
            state.error = null;
        },
        setUserLoading(state) {
            state.isLoading = true;
            state.error = null;
        },
        setUserError(state, action) {
            state.isLoading = false;
            state.error = action.payload;
            state.data = [];
        },
    },
});

export const { setUser, setUserLoading, setUserError } = user.actions;
export default user.reducer;
