import { useEffect } from "react";
import { useWatch } from "react-hook-form";

export const useIncomeCalculation = (control, setValue) => {
  const firstName = useWatch({ control, name: "firstBorrowerName" });
  const firstIncome = useWatch({
    control,
    name: "firstBorrowerFinancialIncome",
  });

  const secondName = useWatch({ control, name: "secondBorrowerName" });
  const secondIncome = useWatch({
    control,
    name: "secondBorrowerFinancialIncome",
  });

  const thirdName = useWatch({ control, name: "thirdBorrowerName" });
  const thirdIncome = useWatch({
    control,
    name: "thirdBorrowerFinancialIncome",
  });

  const roiRate = useWatch({ control, name: "roiRate" });

  useEffect(() => {
    let total = 0;

    if (firstName && firstIncome) {
      total += parseFloat(firstIncome);
      console.log(total);
    }

    if (secondName && secondIncome) {
      total += parseFloat(secondIncome);
    }

    if (thirdName && thirdIncome) {
      total += parseFloat(thirdIncome);
    }

    const repaymentCapacity = roiRate ? (total * parseFloat(roiRate)) / 100 : 0;

    setValue("grandTotalIncome", total);
    setValue("totalRepaymentCapacity", repaymentCapacity);
  }, [
    firstName,
    firstIncome,
    secondName,
    secondIncome,
    thirdName,
    thirdIncome,
    roiRate,
    setValue,
  ]);

  useEffect(() => {
    if (!secondName) {
      setValue("secondBorrowerFinancialIncome", "");
      setValue("thirdBorrowerName", "");
      if (!thirdName) {
        setValue("thirdBorrowerFinancialIncome", "");
      }
    }

    if (!thirdName) {
      setValue("thirdBorrowerFinancialIncome", "");
    }
  }, [secondName, thirdName, setValue]);

  return {
    firstName,
    secondName,
    thirdName,
  };
};
