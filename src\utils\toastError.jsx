import { TriangleAlert } from "lucide-react";
import { toast } from "sonner";

export const ToastError = (message) => {
  if (!message) return;

  toast.custom((id) => (
    <div
      key={id}
      className="w-full max-w-sm rounded-md bg-red-100 text-black p-4 shadow-md border border-red-300 cursor-pointer"
      onClick={() => toast.dismiss(id)}
    >
      <div className="font-semibold">Error</div>
      <div className="flex flex-row gap-2 items-center">
        <span>
          <TriangleAlert />
        </span>
        <div className="text-sm">{message}</div>
      </div>
    </div>
  ));
};
