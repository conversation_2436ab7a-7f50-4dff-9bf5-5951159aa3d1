/* eslint-disable react/prop-types */
import { NavLink } from "react-router-dom";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronRight } from "lucide-react";

export function NavMain({ items }) {
  return (
    <SidebarGroup className="space-y-1">
      <SidebarMenu className="space-y-1">
        {items.map((item) => {
          const isParentActive = item.items?.some((subItem) =>
            location.pathname.startsWith(subItem.url)
          );

          const hasChildren = item.items?.length > 0;
          const isDropdown = !item.url && hasChildren;

          return (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={isParentActive}
              className="group/collapsible"
            >
              <SidebarMenuItem className="relative">
                <CollapsibleTrigger asChild>
                  {item.url && !isDropdown ? (
                    <NavLink to={item.url}>
                      {({ isActive }) => {
                        const active = isActive || isParentActive;
                        return (
                          <SidebarMenuButton
                            tooltip={item.title}
                            className={`group/menu-button relative px-4 py-3.5 my-1 rounded-xl transition-colors duration-200 ${
                              active
                                ? "bg-gradient-to-r from-sidebar-active-color to-primary-color text-white font-semibold shadow-lg shadow-sidebar-active-color/25 border border-sidebar-active-color/20"
                                : "hover:bg-gray-100 hover:text-sidebar-active-color text-gray-700"
                            } ${
                              active
                                ? "before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:w-1 before:h-8 before:bg-white before:rounded-r-full before:shadow-sm"
                                : ""
                            }`}
                          >
                            {item.icon && (
                              <span
                                className={`transition-colors duration-200 ${
                                  active
                                    ? "text-white drop-shadow-sm"
                                    : "text-gray-500 group-hover/menu-button:text-sidebar-active-color"
                                }`}
                              >
                                <item.icon
                                  size={20}
                                  strokeWidth={active ? 2.5 : 2}
                                />
                              </span>
                            )}
                            <span
                              className={`ml-3 transition-colors duration-200 ${
                                active
                                  ? "text-white font-semibold drop-shadow-sm"
                                  : "text-gray-700 group-hover/menu-button:text-sidebar-active-color"
                              }`}
                            >
                              {item.title}
                            </span>
                            {hasChildren ? (
                              <ChevronRight
                                className={`ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 ${
                                  active
                                    ? "text-white"
                                    : "text-gray-400 group-hover/menu-button:text-sidebar-active-color"
                                }`}
                                size={16}
                              />
                            ) : null}
                            {active && (
                              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-xl pointer-events-none" />
                            )}
                          </SidebarMenuButton>
                        );
                      }}
                    </NavLink>
                  ) : (
                    <SidebarMenuButton
                      tooltip={item.title}
                      className={`group/menu-button relative px-4 py-3.5 my-1 rounded-xl transition-colors duration-200 ${
                        isParentActive
                          ? "bg-gradient-to-r from-sidebar-active-color to-primary-color text-white font-semibold shadow-lg shadow-sidebar-active-color/25 border border-sidebar-active-color/20"
                          : "hover:bg-gray-100 hover:text-sidebar-active-color text-gray-700"
                      } ${
                        isParentActive
                          ? "before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:w-1 before:h-8 before:bg-white before:rounded-r-full before:shadow-sm"
                          : ""
                      }`}
                    >
                      {item.icon && (
                        <span
                          className={`transition-colors duration-200 ${
                            isParentActive
                              ? "text-white drop-shadow-sm"
                              : "text-gray-500 group-hover/menu-button:text-sidebar-active-color"
                          }`}
                        >
                          <item.icon
                            size={20}
                            strokeWidth={isParentActive ? 2.5 : 2}
                          />
                        </span>
                      )}
                      <span
                        className={`ml-3 transition-colors duration-200 ${
                          isParentActive
                            ? "text-white font-semibold drop-shadow-sm"
                            : "text-gray-700 group-hover/menu-button:text-sidebar-active-color"
                        }`}
                      >
                        {item.title}
                      </span>
                      {hasChildren ? (
                        <ChevronRight
                          className={`ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 ${
                            isParentActive
                              ? "text-white"
                              : "text-gray-400 group-hover/menu-button:text-sidebar-active-color"
                          }`}
                          size={16}
                        />
                      ) : null}
                      {isParentActive && (
                        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent rounded-xl pointer-events-none" />
                      )}
                    </SidebarMenuButton>
                  )}
                </CollapsibleTrigger>

                {hasChildren && (
                  <CollapsibleContent>
                    <SidebarMenuSub className="ml-4 pl-4 border-l-2 border-gradient-to-b from-sidebar-active-color/30 to-gray-200/50 space-y-1">
                      {item.items.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <NavLink to={subItem.url}>
                            {({ isActive }) => (
                              <SidebarMenuSubButton
                                className={`w-full px-4 py-2.5 my-1 rounded-lg transition-colors duration-200 ${
                                  isActive
                                    ? "bg-gradient-to-r from-sidebar-active-color/10 to-primary-color/5 text-sidebar-active-color font-semibold border border-sidebar-active-color/20 shadow-sm"
                                    : "hover:bg-gray-50 hover:text-sidebar-active-color text-gray-600"
                                } relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:w-0.5 before:h-4 ${
                                  isActive
                                    ? "before:bg-sidebar-active-color"
                                    : "before:bg-transparent hover:before:bg-sidebar-active-color/50"
                                } before:rounded-r-full before:transition-colors before:duration-200`}
                              >
                                <span className="relative z-10 truncate text-sm">
                                  {subItem.title}
                                </span>
                                {isActive && (
                                  <div className="absolute inset-0 bg-gradient-to-r from-sidebar-active-color/5 to-transparent rounded-lg pointer-events-none" />
                                )}
                              </SidebarMenuSubButton>
                            )}
                          </NavLink>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                )}
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
