import { Route, Routes } from "react-router-dom";
import { ROUTES } from "../../constants/route";
import DashboardPage from "../Dashboard/Dashaboard";
import PrimaryApplication from "../PrimaryApplication/PrimaryApplication";
import BranchManagement from "../BranchManagement/BranchManagement";
import AddBranch from "../BranchManagement/AddBranch";
import ApplicationList from "../ApplicationList/ApplicationList";
import EditBranch from "../BranchManagement/EditBranch";
import ProductManagement from "../ProductManagement/ProductManagement";
import AddProduct from "../ProductManagement/AddProduct";
import EditProduct from "../ProductManagement/EditProduct";
import UserManagement from "../UserManagment/UserManagement";
import AddUser from "../UserManagment/AddUser";
import LoanApplicationPage from "../LoanApplication/LoanApplicationPage";
import SchemaDemo from "../SchemaDemo/SchemaDemo";
// import HousingLoanForm from "../LoanApplications/HousingLoan";

const Routing = () => {
  return (
    <Routes>
      <Route index element={<DashboardPage />} />
      <Route>
        <Route
          index
          path={ROUTES.PRIMARY_APPLICATION_FORM}
          element={<PrimaryApplication />}
        />
        <Route
          path={ROUTES.PRIMARY_APPLICATION_LIST}
          element={<ApplicationList />}
        />
      </Route>
      <Route path={ROUTES.BRANCH_MANAGEMENT} element={<BranchManagement />} />
      <Route path={ROUTES.ADD_BRANCH_MANAGEMENT} element={<AddBranch />} />
      <Route path={ROUTES.EDIT_BRANCH_MANAGEMENT} element={<EditBranch />} />
      <Route path={ROUTES.PRODUCT_MANAGEMENT} element={<ProductManagement />} />
      <Route path={ROUTES.ADD_PRODUCT_MANAGEMENT} element={<AddProduct />} />
      <Route path={ROUTES.EDIT_PRODUCT_MANAGEMENT} element={<EditProduct />} />
      <Route path={ROUTES.USER_MANAGEMENT} element={<UserManagement />} />
      <Route path={ROUTES.ADD_USER_MANAGEMENT} element={<AddUser />} />
      <Route
        path={ROUTES.HOUSING_LOAN_FORM}
        element={<LoanApplicationPage />}
      />
      <Route path="/schema-demo" element={<SchemaDemo />} />
    </Routes>
  );
};

export default Routing;
