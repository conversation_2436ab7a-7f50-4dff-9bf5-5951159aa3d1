import { createSlice } from "@reduxjs/toolkit";

const primaryApplication = createSlice({
  name: "primaryApplication",
  initialState: {
    data: [],
    applications: [],
    currentPage: 0,
    totalPages: 0,
    totalApplications: 0,
    loanTypes: [],
    isLoading: false,
    error: null,
  },
  reducers: {
    setPrimaryApplication(state, action) {
      Object.entries(action.payload).forEach(([key, value]) => {
        if (key in state) {
          state[key] = value;
        }
      });
      state.isLoading = false;
      state.error = null;
    },
    setPrimaryApplicationLoading(state) {
      state.isLoading = true;
      state.error = null;
    },
    setPrimaryApplicationError(state, action) {
      state.isLoading = false;
      state.error = action.payload;
      state.data = [];
    },
  },
});

export const {
  setPrimaryApplication,
  setPrimaryApplicationLoading,
  setPrimaryApplicationError,
} = primaryApplication.actions;
export default primaryApplication.reducer;
