const documentsList = [
  "Application acceptance letter",
  "Evidence applicable to the Constitution",
  "KYC of borrowers and guarantors",
  "GST Registration Certificate / Udyam <PERSON> / Copy of Government Registration related to the business",
  "Borrower's IT returns, SOI and balance sheet/audit report/income statement for the last 1/2/3 years",
  "Passport size photographs – 3 (of applicants and guarantors)",
  "Original salary slip of the last 3 months and copy of Form No. 16",
  "Copy of contract/construction estimate/quotation",
  "Copy of latest stock statement and collection list 90 days/more than 90 days",
  "Repayment capacity statement regarding loan repayment",
  "GST Annual Return / GSTR-3 B",
  "Provisional balance sheet for the current year for business",
  "Latest ROC search report of LLP/Company",
  "CIC report of borrower and guarantor",
  "Project report for a new business",
  "Last six months' statements of loan, savings and current accounts from other banks",
  "The borrower and the guarantor will have to obtain CKYC.",
  "Copy of licenses required under applicable laws like Food/Drug/Pollution/Factory Act/Shop Act etc.",
  "To obtain estimated balance for amounts below Rs. 50.00 lakhs",
  "A valuation report of the property to be mortgaged will have to be obtained.",
  "CMA data on amounts of Rs. 50.00 lakhs or more",
  "GIDC 2R Permission (where required)",
  "Copy of RERA registration in case of need",
  "Applicant's AIS (if required)",
  "Property file to be given in mortgage",
  "Loan Application Form",
  "Obtain the title report of the property to be mortgaged and keep it on file, after which we will repay the loan.",
  "We will obtain quotations, bills and receipts related to machinery/vehicles and keep them on file.",
  "We will obtain and keep on file insurance for machinery/vehicles and immovable property.",
  "We will register with CERSAI and keep the report on file.",
];

export const DEFAULT_VALUES = {
  fname: "",
  mname: "",
  lname: "",
  phoneCode: "+91",
  gender: "",
  motherName: "",
  dateOfBirth: "",
  maritalStatus: "",
  nationality: "",
  phoneNumber: "",
  alternatePhoneCode: "+91",
  alternatePhoneNumber: "",
  loanProductId: "",
  email: "",
  applicationType: "something",
  occupationType: "",
  occupationTypeName: "",
  constitution: "",
  dateOfBusinessStart: "",
  purposeOfLoan: "",
  applicantExperience: "",
  applicantDetails: [],
  totalRepaymentCapacity: "",
  grandTotalIncome: "",
  firstBorrowerIncome: "",
  secondBorrowerIncome: "",
  thirdBorrowerIncome: "",
  firstBorrowerName: "",
  secondBorrowerName: "",
  thirdBorrowerName: "",
  businessAddressLineFirst: "",
  businessAddressLineSecond: "",
  businessAddressLineThird: "",
  businessAddressCity: "",
  businessAddressState: "",
  businessAddressPincode: "",
  businessAddressCountry: "",
  homeAddressLineFirst: "",
  homeAddressLineSecond: "",
  homeAddressLineThird: "",
  homeAddressCity: "",
  homeAddressState: "",
  homeAddressPincode: "",
  homeAddressCountry: "",
  //Loan Details Values
  gstNumber: "",
  panNumber: "",
  businessRegistrationNumber: "",
  moratoriumPush: "",
  businessSector: "",
  typeOfLoan: "",
  loanAmount: "",
  interestType: "",
  rateOfInterest: "",
  //Collateral Details Values
  propertyOwnerName: "",
  propertyAddress: "",
  propertyType: "",
  landArea: "",
  constructionArea: "",
  valuation: "",
  valuationDate: "",

  //Document Details Values
  documents: documentsList.map((doc) => ({
    documentName: doc,
    value: "",
  })),
};
