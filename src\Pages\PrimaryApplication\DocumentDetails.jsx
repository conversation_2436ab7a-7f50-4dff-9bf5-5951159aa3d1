import { useFieldArray, useWatch } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { RadioGroup, RadioGroupItem } from "../../components/ui/radio-group";
import AutoScrollArea from "../../components/custom/AutoScrollArea";

const options = ["Yes", "No", "Maybe"];

const DocumentDetails = ({ control }) => {
  const { fields } = useFieldArray({
    control,
    name: "documents",
  });

  const watchedDocuments = useWatch({ control, name: "documents" });

  return (
    <AutoScrollArea ids={["page-header", "progressBar"]}>
      <div className="flex flex-col gap-6 px-5">
        {fields.map((field, index) => (
          <FormField
            key={field.id}
            control={control}
            name={`documents.${index}.value`}
            render={({ field: radioField }) => (
              <FormItem className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-8">
                {/* Left column (fixed width) */}
                <div className=" w-[450px] md:[500px] 2xl:w-[800px]  font-medium">
                  <FormLabel>
                    {index + 1} - {watchedDocuments?.[index]?.documentName}
                  </FormLabel>
                </div>

                {/* Right column (radio buttons) */}
                <div className="flex-1">
                  <FormControl>
                    <RadioGroup
                      onValueChange={radioField.onChange}
                      value={radioField.value}
                      className="flex gap-6"
                    >
                      {options.map((option) => (
                        <FormItem
                          key={option}
                          className="flex items-center space-x-2"
                        >
                          <FormControl>
                            <RadioGroupItem value={option} />
                          </FormControl>
                          <FormLabel className="text-sm font-normal">
                            {option}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />
        ))}
      </div>
    </AutoScrollArea>
  );
};

export default DocumentDetails;
