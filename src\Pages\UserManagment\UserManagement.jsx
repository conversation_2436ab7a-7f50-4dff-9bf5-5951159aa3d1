import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { replacePathParams } from "../../lib/utils";
import { ROUTES } from "../../constants/route";
import { useGetUsersMutation } from "../../store/actions";
import usePagination from "../../hooks/usePagination";
import { useSelector } from "react-redux";
import Loader from "../../components/custom/Loader";
import CustomPagination from "../../components/custom/Pagination";

const UserManagement = () => {
  const navigate = useNavigate();

  const handleAdd = () => {
    navigate(replacePathParams(ROUTES.ADD_USER_MANAGEMENT));
  };

  const [getUsers] = useGetUsersMutation();

  const { data, isLoading, totalPages } = useSelector((state) => state.user);
  console.log("🚀 ~ UserManagement ~ data:", data);

  const { page, setPage, limit, setLimit } = usePagination(getUsers, {
    search: "",
    status: "",
  });

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className="p-6 flex-col flex-1 h-screen overflow-y-hidden relative">
      {/* Main Content */}
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <div className="flex flex-col gap-4">
          <div className="flex flex-1 justify-end">
            <Button
              onClick={() => handleAdd()}
              className="bg-gradient-to-r from-primary-color to-sidebar-active-color hover:from-primary-color/90 hover:to-sidebar-active-color/90 text-white font-semibold px-6 py-2.5 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Add User
            </Button>
          </div>
          {/* <Table>
            <TableHeader>
              <TableRow className="border-0">
                <TableHead className={"border-0 rounded-tl-md rounded-bl-md"}>
                  Branch Code
                </TableHead>
                <TableHead>Branch Name</TableHead>
                <TableHead>Branch Address</TableHead>
                <TableHead className={"border-0 rounded-tr-md rounded-br-md"}>
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item?.branch_code}</TableCell>
                  <TableCell>{item?.branch_name}</TableCell>

                  <TableCell>{item?.branch_address}</TableCell>

                  <TableCell>
                    <div className="flex flex-row gap-1.5 items-center">
                      <Button
                        size="icon"
                        className="text-amber-400 border-amber-400 hover:border-amber-400/70 hover:text-amber-400/70"
                        variant="outline"
                        onClick={() => {
                          handleEdit(item._id);
                        }}
                      >
                        <Pencil />
                      </Button>
                      <Button
                        size="icon"
                        className="text-red-500 border-red-500 hover:border-red-500/70 hover:text-red-500/70"
                        variant="outline"
                        onClick={() => handleDelete(item._id)}
                      >
                        <Trash2 />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table> */}

          <CustomPagination
            currentPage={page}
            totalPages={totalPages}
            limit={limit}
            onPageChange={setPage}
            onLimitChange={setLimit}
          />
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
