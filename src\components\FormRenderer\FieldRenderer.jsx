/* eslint-disable react/prop-types */
// components/form/FieldRenderer.jsx
import InputField from "@/components/custom/InputField";
import SelectField from "@/components/custom/SelectInput";
import <PERSON>Field from "@/components/custom/RadioButton";
import Textarea<PERSON>ield from "@/components/custom/TextAreaField";
import Checkbox<PERSON>ield from "@/components/custom/CheckboxField";
import DateField from "@/components/custom/DateField";
import { useWatch } from "react-hook-form";
import TableField from "../custom/TableField";

/**
 * Recursively renders a field based on schema
 * @param {object} field - A field from the JSON schema
 * @param {object} control - RHF control object
 * @param {string} prefix - Optional prefix for nested field paths
 * @param {object} fieldOptions - Optional map of dynamic options per field
 */
export default function FieldRenderer({
  field,
  control,
  prefix = "",
  fieldOptions = {},
  colSpan = 1,
  isAdditional,
}) {
  const path = prefix ? `${prefix}.${field.name}` : field.name;

  // Conditional rendering logic
  const dependsOn = field.dependsOn;
  if (dependsOn) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const dependentValue = useWatch({
      control,
      name: dependsOn.name,
      defaultValue: undefined,
    });

    if (dependentValue !== dependsOn.value) {
      return null;
    }
  }

  // 🔽 ADD THIS: for additional applicant fields only
  if (field.onlyForAdditionalApplicant && !isAdditional) {
    return null;
  }

  const dynamicOptions = fieldOptions[field.name]; // 👈 dynamically injected options
  const optionsToUse = dynamicOptions || field.options || [];

  const sharedProps = {
    control,
    name: path,
    label: field.label,
    placeholder: field.placeholder || "",
    isRequired: field.required || false,
    disabled: field.lockedForPrimary || false,
    options: optionsToUse, // ✅ Pass resolved options
    columns: field.columns || [], // ✅ For TableField
    rows: field.rows || 1,
    description: field.description, // ✅ For CheckboxField description
    helpText: field.helpText, // ✅ For help text
  };

  const flexWidth = {
    1: "w-full md:w-[calc(50%-0.5rem)]", // 1 column = 50% width minus gap
    2: "w-full", // 2 columns = full width
  }[colSpan];

  if (field.type === "group") {
    return (
      <div className={`${flexWidth} mb-4`}>
        <div className="flex flex-col gap-4 rounded-lg">
          <label className="text-sm font-medium">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </label>

          <div className={`flex flex-wrap gap-4 ${field.gridClass || ""}`}>
            {field.fields.map((nestedField) => (
              <FieldRenderer
                key={nestedField.name}
                field={nestedField}
                control={control}
                prefix={path}
                fieldOptions={fieldOptions}
                colSpan={nestedField.colSpan || 1}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  const fieldTypeComponentMap = {
    text: InputField,
    number: InputField,
    date: DateField,
    password: InputField,
    textarea: TextareaField,
    select: SelectField,
    radio: RadioField,
    checkbox: CheckboxField,
    checkboxGroup: CheckboxField,
    email: InputField,
    tel: InputField,
    table: TableField,
  };

  const FieldComponent = fieldTypeComponentMap[field.type];

  if (!FieldComponent) {
    console.warn(`Unsupported field type: ${field.type}`);
    return null;
  }

  return (
    <div className={`${flexWidth} mb-4 ${field.customClass || ""}`}>
      <FieldComponent {...sharedProps} />
    </div>
  );
}
