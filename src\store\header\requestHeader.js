export const getHeaders = (customHeaders = {}) => {
  const token = sessionStorage.getItem("token");

  if (!token) {
    return {
      Accept: "application/json",
      "Content-Type": "application/json",
      ...customHeaders,
    };
  } else {
    return {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
      "Content-Type": "application/json",
      ...customHeaders,
    };
  }
};
