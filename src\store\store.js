import { configureStore } from "@reduxjs/toolkit";
import {
  authentication,
  branch,
  primaryApplication,
  location,
  product,
  user
} from "./reducer";
import { authenticationApi } from "./actions/authentication/authentication";
import { branchApi } from "./actions/branch/branch";
import { primaryApplicationApi } from "./actions/primaryApplication/primaryApplication";
import { locationApi } from "./actions/location/location";
import { productApi } from "./actions/product/product";
import { userApi } from "./actions/authentication/user";

export const setupStore = () => {
  return configureStore({
    reducer: {
      [authenticationApi.reducerPath]: authenticationApi.reducer,
      [branchApi.reducerPath]: branchApi.reducer,
      [primaryApplicationApi.reducerPath]: primaryApplicationApi.reducer,
      [locationApi.reducerPath]: locationApi.reducer,
      [productApi.reducerPath]: productApi.reducer,
      [userApi.reducerPath]: userApi.reducer,

      authentication: authentication,
      branch: branch,
      primaryApplication: primaryApplication,
      location: location,
      product: product,
      user: user,
    },
    middleware: (getDefaultMiddleware) => {
      return getDefaultMiddleware()
        .concat(authenticationApi.middleware)
        .concat(primaryApplicationApi.middleware)
        .concat(branchApi.middleware)
        .concat(productApi.middleware)
        .concat(locationApi.middleware)
        .concat(userApi.middleware);
    },
  });
};

export const AppStore = setupStore;
export const AppDispatch = AppStore["dispatch"];
