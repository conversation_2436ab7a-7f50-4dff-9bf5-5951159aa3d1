// Housing Loan Schema based on the provided form
const housingLoanDetailedSchema = {
  loanCode: "HL",
  title: "Home Loan Application",
  description: "Apply for a home loan to purchase, construct, or renovate your dream home",
  subtitle: "Complete all sections accurately to ensure quick processing of your application",
  category: "Housing Finance",
  estimatedTime: "25-30 minutes",
  sections: [
    {
      sectionId: "application_header",
      sectionTitle: "Application Information",
      description: "Basic application details and type",
      iconName: "FileText",
      iconColor: "text-blue-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "applicationNumber",
          label: "Application Number",
          type: "text",
          required: true,
          placeholder: "Enter application number",
          colSpan: 1,
        },
        {
          name: "branchCode",
          label: "Branch Code",
          type: "text",
          required: true,
          placeholder: "Enter branch code",
          colSpan: 1,
        },
        {
          name: "accountType",
          label: "Type of A/c",
          type: "select",
          required: true,
          options: [
            { value: "housing", label: "Housing" },
            { value: "other", label: "Other" },
          ],
          colSpan: 1,
        },
        {
          name: "riskCategory",
          label: "Risk Category",
          type: "checkbox",
          options: [
            { value: "high", label: "High" },
            { value: "low", label: "Low" },
          ],
          colSpan: 1,
        },
      ],
    },
    {
      sectionId: "applicant_details",
      sectionTitle: "Applicant Details",
      description: "Primary and co-applicant information",
      iconName: "Users",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 lg:grid-cols-2",
      fields: [
        // Primary Applicant Section
        {
          name: "primaryApplicant",
          label: "Primary Applicant (Applicant-1)",
          type: "group",
          colSpan: 1,
          fields: [
            {
              name: "name",
              label: "Name",
              type: "text",
              required: true,
              placeholder: "Enter full name",
            },
            {
              name: "customerNumber",
              label: "Customer No. (UCG)",
              type: "text",
              placeholder: "Enter customer number",
            },
            {
              name: "ckycrNumber",
              label: "CKYCR No.",
              type: "text",
              placeholder: "Enter CKYCR number",
            },
            {
              name: "gender",
              label: "Gender",
              type: "radio",
              required: true,
              options: [
                { value: "male", label: "Male" },
                { value: "female", label: "Female" },
              ],
            },
            {
              name: "contactNumber",
              label: "Contact Number",
              type: "text",
              required: true,
              placeholder: "Enter contact number",
            },
            {
              name: "dateOfBirth",
              label: "Date of Birth",
              type: "date",
              required: true,
            },
            {
              name: "panNumber",
              label: "PAN",
              type: "text",
              required: true,
              placeholder: "Enter PAN number",
              pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}",
            },
            {
              name: "aadhaarNumber",
              label: "Aadhaar Number",
              type: "text",
              placeholder: "Enter Aadhaar number",
              pattern: "[0-9]{12}",
            },
            {
              name: "nationality",
              label: "Nationality",
              type: "select",
              required: true,
              options: [
                { value: "indian", label: "IN - Indian" },
                { value: "other", label: "Other" },
              ],
            },
            {
              name: "occupation",
              label: "Occupation",
              type: "select",
              required: true,
              options: [
                { value: "service", label: "Service" },
                { value: "business", label: "Business" },
                { value: "other", label: "Other" },
              ],
            },
          ],
        },
        // Co-Applicant Section
        {
          name: "coApplicant",
          label: "Co-Applicant (Applicant-2)",
          type: "group",
          colSpan: 1,
          fields: [
            {
              name: "name",
              label: "Name",
              type: "text",
              placeholder: "Enter full name",
            },
            {
              name: "gender",
              label: "Gender",
              type: "radio",
              options: [
                { value: "male", label: "Male" },
                { value: "female", label: "Female" },
              ],
            },
            {
              name: "contactNumber",
              label: "Contact Number",
              type: "text",
              placeholder: "Enter contact number",
            },
            {
              name: "dateOfBirth",
              label: "Date of Birth",
              type: "date",
            },
            {
              name: "panNumber",
              label: "PAN",
              type: "text",
              placeholder: "Enter PAN number",
              pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}",
            },
            {
              name: "aadhaarNumber",
              label: "Aadhaar Number",
              type: "text",
              placeholder: "Enter Aadhaar number",
              pattern: "[0-9]{12}",
            },
            {
              name: "nationality",
              label: "Nationality",
              type: "select",
              options: [
                { value: "indian", label: "IN - Indian" },
                { value: "other", label: "Other" },
              ],
            },
            {
              name: "occupation",
              label: "Occupation",
              type: "select",
              options: [
                { value: "service", label: "Service" },
                { value: "business", label: "Business" },
                { value: "other", label: "Other" },
              ],
            },
          ],
        },
      ],
    },
    {
      sectionId: "address_details",
      sectionTitle: "Address Details",
      description: "Residential and communication addresses",
      iconName: "MapPin",
      iconColor: "text-purple-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "addressInfo",
          label: "Address Information",
          type: "group",
          colSpan: "full",
          fields: [
            {
              name: "permanentAddress",
              label: "Permanent Address",
              type: "textarea",
              required: true,
              placeholder: "Enter permanent address",
              rows: 3,
            },
            {
              name: "communicationAddress",
              label: "Communication Address",
              type: "textarea",
              placeholder: "Enter communication address (if different)",
              rows: 3,
            },
            {
              name: "sameAsPermanent",
              label: "Same as Permanent Address",
              type: "checkbox",
              options: [
                { value: "yes", label: "Yes, communication address is same as permanent address" },
              ],
            },
          ],
        },
      ],
    },
    {
      sectionId: "family_details",
      sectionTitle: "Family Details",
      description: "Information about family members and dependents",
      iconName: "Users",
      iconColor: "text-orange-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "familyMembers",
          label: "Family Members Information",
          type: "table",
          colSpan: "full",
          rows: 5,
          columns: [
            { name: "name", label: "Name and Relationship", type: "text", required: true },
            { name: "relationshipType", label: "Relationship Type", type: "text" },
            { name: "age", label: "Age", type: "number" },
            { name: "occupation", label: "Occupation", type: "text" },
            { name: "monthlyIncome", label: "Monthly Income", type: "number" },
          ],
          helpText: "Add details of all family members living with you",
        },
      ],
    },
    {
      sectionId: "guarantor_details",
      sectionTitle: "Guarantor Details",
      description: "Information about guarantors (if applicable)",
      iconName: "Shield",
      iconColor: "text-red-600",
      required: false,
      gridClass: "grid-cols-1 lg:grid-cols-2",
      fields: [
        {
          name: "guarantor1",
          label: "Guarantor 1",
          type: "group",
          colSpan: 1,
          fields: [
            {
              name: "name",
              label: "Full Name",
              type: "text",
              placeholder: "Enter guarantor's full name",
            },
            {
              name: "fatherName",
              label: "Father's Name",
              type: "text",
              placeholder: "Enter father's name",
            },
            {
              name: "address",
              label: "Address",
              type: "textarea",
              placeholder: "Enter complete address",
              rows: 3,
            },
            {
              name: "occupation",
              label: "Occupation/Business",
              type: "text",
              placeholder: "Enter occupation or business details",
            },
            {
              name: "monthlyIncome",
              label: "Monthly Income",
              type: "number",
              placeholder: "Enter monthly income",
            },
            {
              name: "relationshipWithApplicant",
              label: "Relationship with Applicant",
              type: "text",
              placeholder: "Enter relationship",
            },
          ],
        },
        {
          name: "guarantor2",
          label: "Guarantor 2",
          type: "group",
          colSpan: 1,
          fields: [
            {
              name: "name",
              label: "Full Name",
              type: "text",
              placeholder: "Enter guarantor's full name",
            },
            {
              name: "fatherName",
              label: "Father's Name",
              type: "text",
              placeholder: "Enter father's name",
            },
            {
              name: "address",
              label: "Address",
              type: "textarea",
              placeholder: "Enter complete address",
              rows: 3,
            },
            {
              name: "occupation",
              label: "Occupation/Business",
              type: "text",
              placeholder: "Enter occupation or business details",
            },
            {
              name: "monthlyIncome",
              label: "Monthly Income",
              type: "number",
              placeholder: "Enter monthly income",
            },
            {
              name: "relationshipWithApplicant",
              label: "Relationship with Applicant",
              type: "text",
              placeholder: "Enter relationship",
            },
          ],
        },
      ],
    },
    {
      sectionId: "property_details",
      sectionTitle: "Property Details",
      description: "Information about the property to be purchased/constructed",
      iconName: "Home",
      iconColor: "text-indigo-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "propertyType",
          label: "Property Type",
          type: "radio",
          required: true,
          options: [
            { value: "purchase", label: "Purchase of Property" },
            { value: "construction", label: "Construction of Property" },
            { value: "renovation", label: "Renovation/Extension" },
          ],
          colSpan: 2,
        },
        {
          name: "propertyLocation",
          label: "Property Location",
          type: "textarea",
          required: true,
          placeholder: "Enter complete property address",
          rows: 3,
          colSpan: 2,
        },
        {
          name: "propertyValue",
          label: "Property Value/Cost",
          type: "number",
          required: true,
          placeholder: "Enter property value in Rs.",
          colSpan: 1,
        },
        {
          name: "loanAmountRequired",
          label: "Loan Amount Required",
          type: "number",
          required: true,
          placeholder: "Enter loan amount required",
          colSpan: 1,
        },
        {
          name: "ownContribution",
          label: "Own Contribution",
          type: "number",
          required: true,
          placeholder: "Enter your contribution amount",
          colSpan: 1,
        },
        {
          name: "loanTenure",
          label: "Loan Tenure (Years)",
          type: "number",
          required: true,
          placeholder: "Enter loan tenure in years",
          colSpan: 1,
        },
      ],
    },
    {
      sectionId: "income_details",
      sectionTitle: "Income Details",
      description: "Monthly income and financial information",
      iconName: "DollarSign",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "monthlyIncome",
          label: "Monthly Income",
          type: "number",
          required: true,
          placeholder: "Enter monthly income",
          colSpan: 1,
        },
        {
          name: "otherIncome",
          label: "Other Income",
          type: "number",
          placeholder: "Enter other income (if any)",
          colSpan: 1,
        },
        {
          name: "totalMonthlyIncome",
          label: "Total Monthly Income",
          type: "number",
          placeholder: "Total will be calculated",
          disabled: true,
          colSpan: 1,
        },
        {
          name: "monthlyExpenses",
          label: "Monthly Expenses",
          type: "number",
          required: true,
          placeholder: "Enter monthly expenses",
          colSpan: 1,
        },
        {
          name: "existingLoans",
          label: "Existing Loan EMIs",
          type: "number",
          placeholder: "Enter existing loan EMIs",
          colSpan: 1,
        },
        {
          name: "netMonthlyIncome",
          label: "Net Monthly Income",
          type: "number",
          placeholder: "Net income will be calculated",
          disabled: true,
          colSpan: 1,
        },
      ],
    },
    {
      sectionId: "bank_details",
      sectionTitle: "Bank Account Details",
      description: "Banking information for loan processing",
      iconName: "CreditCard",
      iconColor: "text-blue-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "bankName",
          label: "Bank Name",
          type: "text",
          required: true,
          placeholder: "Enter bank name",
          colSpan: 1,
        },
        {
          name: "branchName",
          label: "Branch Name",
          type: "text",
          required: true,
          placeholder: "Enter branch name",
          colSpan: 1,
        },
        {
          name: "accountNumber",
          label: "Account Number",
          type: "text",
          required: true,
          placeholder: "Enter account number",
          colSpan: 1,
        },
        {
          name: "ifscCode",
          label: "IFSC Code",
          type: "text",
          required: true,
          placeholder: "Enter IFSC code",
          colSpan: 1,
        },
        {
          name: "accountType",
          label: "Account Type",
          type: "select",
          required: true,
          options: [
            { value: "savings", label: "Savings Account" },
            { value: "current", label: "Current Account" },
            { value: "salary", label: "Salary Account" },
          ],
          colSpan: 1,
        },
        {
          name: "accountHolderName",
          label: "Account Holder Name",
          type: "text",
          required: true,
          placeholder: "Enter account holder name",
          colSpan: 1,
        },
      ],
    },
    {
      sectionId: "declarations",
      sectionTitle: "Declarations and Undertakings",
      description: "Important declarations and agreements",
      iconName: "FileCheck",
      iconColor: "text-red-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "declarations",
          label: "I hereby declare and undertake that",
          type: "checkbox",
          required: true,
          options: [
            {
              value: "all_info_correct",
              label: "All information provided in this application is true, complete and accurate to the best of my knowledge and belief. I understand that any false information may lead to rejection of my application or cancellation of the loan.",
            },
          ],
          colSpan: "full",
        },
        {
          name: "applicantSignature",
          label: "Applicant's Signature",
          type: "text",
          placeholder: "Digital signature or name",
          required: true,
          colSpan: 1,
        },
        {
          name: "applicationDate",
          label: "Date",
          type: "date",
          required: true,
          colSpan: 1,
        },
        {
          name: "place",
          label: "Place",
          type: "text",
          required: true,
          placeholder: "Enter place of application",
          colSpan: 1,
        },
      ],
    },
  ],
};

export default housingLoanDetailedSchema;
