// src/schemas/index.js
import housingLoanSchema from "./homeLoan";
import housingLoanDetailedSchema from "./housingLoanDetailed";
import educationLoanSchema from "./educationLoan";
import businessLoanSchema from "./businessLoan";

// Schema registry with metadata
export const schemaMap = {
    HL: housingLoanSchema, // Use the detailed housing loan schema
    HL_SIMPLE: housingLoanSchema, // Keep the simple version as backup
    EL: educationLoanSchema,
    BL: businessLoanSchema,
    // Add more loan types as needed
    PL: housingLoanSchema, // Temporary - use housing loan schema for personal loan
    CL: housingLoanSchema, // Temporary - use housing loan schema for car loan
    GL: housingLoanSchema, // Temporary - use housing loan schema for gold loan
};

// Schema metadata for UI display and selection
export const schemaMetadata = {
    HL: {
        code: "HL",
        name: "Home Loan",
        title: "Home Loan Application",
        description: "Apply for a home loan to purchase, construct, or renovate your dream home",
        category: "Housing Finance",
        icon: "🏠",
        color: "blue",
        estimatedTime: "25-30 minutes",
        features: ["Competitive interest rates", "Flexible tenure", "Quick approval", "Comprehensive application"],
        eligibility: ["Age: 21-65 years", "Minimum income: ₹25,000/month", "Good credit score"],
        maxAmount: "₹5 Crores",
        tenure: "Up to 30 years"
    },
    EL: {
        code: "EL",
        name: "Education Loan",
        title: "Education Loan Application",
        description: "Apply for an education loan to fund your higher education dreams",
        category: "Education Finance",
        icon: "🎓",
        color: "green",
        estimatedTime: "10-15 minutes",
        features: ["No collateral for loans up to ₹7.5L", "Moratorium period", "Tax benefits"],
        eligibility: ["Admission to recognized institution", "Co-applicant required", "Good academic record"],
        maxAmount: "₹1 Crore",
        tenure: "Up to 15 years"
    },
    BL: {
        code: "BL",
        name: "Business Loan",
        title: "Business Loan Application",
        description: "Apply for a business loan to grow your enterprise and achieve your business goals",
        category: "Business Finance",
        icon: "💼",
        color: "purple",
        estimatedTime: "20-25 minutes",
        features: ["Minimal documentation", "Quick disbursement", "Flexible repayment"],
        eligibility: ["Business vintage: 2+ years", "Annual turnover: ₹40L+", "Good credit history"],
        maxAmount: "₹5 Crores",
        tenure: "Up to 7 years"
    }
};

// Helper functions
export const getSchemaByCode = (code) => {
    return schemaMap[code] || null;
};

export const getSchemaMetadata = (code) => {
    return schemaMetadata[code] || null;
};

export const getAllSchemas = () => {
    return Object.keys(schemaMap).map(code => ({
        ...schemaMetadata[code],
        schema: schemaMap[code]
    }));
};

export const getSchemasByCategory = (category) => {
    return Object.keys(schemaMetadata)
        .filter(code => schemaMetadata[code].category === category)
        .map(code => ({
            ...schemaMetadata[code],
            schema: schemaMap[code]
        }));
};
