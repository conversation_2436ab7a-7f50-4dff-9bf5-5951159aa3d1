/* eslint-disable react/prop-types */
import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Clock,
  Search,
  Filter,
  ArrowRight,
  CheckCircle,
  Star,
} from "lucide-react";
import { getAllSchemas, getSchemasByCategory } from "@/schemas";

export default function SchemaSelector({ onSchemaSelect, selectedSchema }) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const allSchemas = getAllSchemas();
  const categories = [...new Set(allSchemas.map((schema) => schema.category))];

  // Filter schemas based on search and category
  const filteredSchemas = allSchemas.filter((schema) => {
    const matchesSearch =
      schema.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schema.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      selectedCategory === "all" || schema.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getColorClasses = (color) => {
    const colorMap = {
      blue: "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100",
      green: "border-green-200 bg-green-50 text-green-700 hover:bg-green-100",
      purple:
        "border-purple-200 bg-purple-50 text-purple-700 hover:bg-purple-100",
      orange:
        "border-orange-200 bg-orange-50 text-orange-700 hover:bg-orange-100",
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <div className="w-full min-h-screen bg-gray-50">
      <div className="w-full max-w-none mx-auto px-6 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Choose Your Loan Type
          </h1>
          <p className="text-gray-600 text-xl max-w-3xl mx-auto">
            Select the loan application that best fits your needs and get
            started with your financial journey
          </p>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col lg:flex-row gap-6 mb-12">
          <div className="flex-1">
            <Label htmlFor="search" className="sr-only">
              Search loans
            </Label>
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                id="search"
                placeholder="Search loan types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 py-3 text-base border-2 focus:border-blue-500 rounded-xl"
              />
            </div>
          </div>
          <div className="lg:w-80">
            <Label htmlFor="category" className="sr-only">
              Filter by category
            </Label>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger className="py-3 text-base border-2 focus:border-blue-500 rounded-xl">
                <Filter className="h-5 w-5 mr-2" />
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Schema Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {filteredSchemas.map((schema) => (
            <Card
              key={schema.code}
              className={`relative cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 ${
                selectedSchema === schema.code
                  ? `ring-4 ring-${
                      schema.color
                    }-500 shadow-2xl scale-105 ${getColorClasses(schema.color)}`
                  : "hover:shadow-xl border-2 border-gray-200"
              }`}
              onClick={() => onSchemaSelect(schema.code)}
            >
              {selectedSchema === schema.code && (
                <div className="absolute -top-3 -right-3 bg-green-500 text-white rounded-full p-2 shadow-lg">
                  <CheckCircle className="h-6 w-6" />
                </div>
              )}

              <CardHeader className="pb-6">
                <div className="flex flex-col gap-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-4">
                      <div className="text-4xl">{schema.icon}</div>
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-900">
                          {schema.name}
                        </CardTitle>
                        <Badge
                          variant="outline"
                          className="mt-2 text-sm font-medium px-3 py-1 bg-blue-50 text-blue-800 border-blue-200"
                        >
                          {schema.code}
                        </Badge>
                      </div>
                    </div>
                    <Badge
                      variant="secondary"
                      className="text-sm px-3 py-1 bg-gray-100 text-gray-800 border border-gray-200"
                    >
                      {schema.category}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-6 p-6">
                <CardDescription className="text-base text-gray-700 leading-relaxed">
                  {schema.description}
                </CardDescription>

                {/* Key Features */}
                <div className="space-y-3">
                  <h4 className="text-base font-semibold text-gray-900">
                    Key Features:
                  </h4>
                  <ul className="text-sm text-gray-700 space-y-2">
                    {schema.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <Star className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Loan Details */}
                <div className="grid grid-cols-2 gap-6 py-4 border-t border-gray-100">
                  <div>
                    <span className="text-sm text-gray-500 block mb-1">
                      Max Amount:
                    </span>
                    <p className="font-bold text-lg text-gray-900">
                      {schema.maxAmount}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500 block mb-1">
                      Tenure:
                    </span>
                    <p className="font-bold text-lg text-gray-900">
                      {schema.tenure}
                    </p>
                  </div>
                </div>

                {/* Time Estimate */}
                <div className="flex items-center gap-3 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                  <Clock className="h-4 w-4" />
                  <span>
                    Estimated time: <strong>{schema.estimatedTime}</strong>
                  </span>
                </div>

                {/* Action Button */}
                <Button
                  className={`w-full py-3 text-base font-semibold transition-all duration-200 ${
                    selectedSchema === schema.code
                      ? "bg-green-600 hover:bg-green-700 shadow-lg"
                      : "border-2 hover:border-blue-500 hover:bg-blue-50"
                  }`}
                  variant={
                    selectedSchema === schema.code ? "default" : "outline"
                  }
                  onClick={(e) => {
                    e.stopPropagation();
                    onSchemaSelect(schema.code);
                  }}
                >
                  {selectedSchema === schema.code
                    ? "✓ Selected"
                    : "Select This Loan"}
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredSchemas.length === 0 && (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-6">
              <Search className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">
              No loan types found
            </h3>
            <p className="text-gray-600 text-lg">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
