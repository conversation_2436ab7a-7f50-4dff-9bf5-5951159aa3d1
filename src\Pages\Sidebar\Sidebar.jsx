import {
  NotebookPen,
  Globe,
  House,
  ChartSpline,
  Users,
  Settings,
  FileDiff,
  Boxes,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { NavMain } from "./NavMain";
import { NavUser } from "./NavUser";
import Side<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "./SidebarHeaderLogo";

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: House,
      items: [],
    },
    {
      title: "Primary Applications",
      url: null,
      icon: FileDiff,
      items: [
        {
          title: "Primally Application form",
          url: "/primary-application/form",
        },
        {
          title: "Application List",
          url: "/primary-application/list",
        },
      ],
    },
    {
      title: "Loan Applications",
      url: "/loan-application",
      icon: NotebookPen,
      items: [],
    },
    {
      title: "Product Management",
      url: "/product-management",
      icon: Boxes,
      items: [],
    },
    {
      title: "Reports & Analytics",
      url: "/reports",
      icon: ChartSpline,
      items: [],
    },
    {
      title: "Branch Management",
      url: "/branch-management",
      icon: Globe,
      items: [],
    },

    {
      title: "User Management",
      url: "/user-management",
      icon: Users,
      items: [],
    },

    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
      items: [],
    },
  ],
};

export function SidebarComponent({ ...props }) {
  return (
    <Sidebar
      collapsible="icon"
      className="border-r-0 shadow-lg bg-gradient-to-b from-white via-gray-50/50 to-gray-100/30 backdrop-blur-sm"
      {...props}
    >
      <SidebarHeader className="p-0 bg-gradient-to-r from-primary-color/5 to-sidebar-active-color/5 border-b border-gray-200/50">
        <SidebarHeaderLogo />
      </SidebarHeader>
      <SidebarContent className="px-3 py-4 space-y-2 sidebar-content">
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter className="border-t border-gray-200/50 bg-gradient-to-r from-gray-50/50 to-white/50 backdrop-blur-sm">
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
