import { useLocation } from "react-router-dom";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import pageTitles from "../../constants/pageTitle";
import headerGraphics from "../../assets/header-graphic.png";

const Header = () => {
  const location = useLocation();
  const pathSegments = location.pathname.split("/").filter(Boolean); // Split and remove empty strings

  let currentTitle = "Dashboard"; // Default title

  if (pathSegments.length === 1) {
    // If there's only a parent route
    currentTitle = pageTitles[`/${pathSegments[0]}`] || "Dashboard";
  } else if (pathSegments.length > 1) {
    // If there's a parent + sub-route
    const parentRoute = `/${pathSegments[0]}`;
    const subRoute = `/${pathSegments.join("/")}`;

    const parentTitle = pageTitles[parentRoute] || "Unknown";
    const subTitle = pageTitles[subRoute] || "Unknown";

    currentTitle = `${parentTitle} - ${subTitle}`;
  }

  const getPageDescription = (pathname) => {
    const descriptions = {
      "/": "Welcome back! Monitor your loan management system performance.",
      "/primary-application/form":
        "Complete your loan application in simple steps.",
      "/primary-application/list": "View and manage all primary applications.",
      "/user-management": "Manage system users, roles, and permissions.",
      "/branch-management": "Manage bank branches and locations.",
      "/product-management": "Configure loan products and settings.",
      "/loan-application": "Process and review loan applications.",
      "/reports": "View analytics and generate reports.",
      "/settings": "Configure system settings and preferences.",
    };

    return (
      descriptions[pathname] || "Welcome back! Here's what's happening today."
    );
  };

  return (
    <header
      className="mx-6 mt-6 flex h-24 shrink-0 items-center gap-4 px-6 rounded-2xl bg-gradient-to-r from-primary-color via-sidebar-active-color to-primary-color shadow-lg shadow-primary-color/20 border border-primary-color/20 backdrop-blur-sm relative overflow-hidden"
      id="page-header"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-transparent to-white/5 pointer-events-none" />
      <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12" />

      <SidebarTrigger className="-ml-1 text-white-color md:hidden hover:bg-white/10 rounded-lg p-2 transition-colors duration-200" />

      <div className="flex flex-col relative z-10">
        <h1 className="text-white-color font-bold text-2xl tracking-wide drop-shadow-sm">
          {currentTitle}
        </h1>
        <p className="text-white-color/80 text-sm font-medium">
          {getPageDescription(location.pathname)}
        </p>
      </div>

      {/* Right side decorative elements */}
      <div className="ml-auto flex items-center gap-3 relative z-10">
        <div className="hidden md:flex items-center gap-2 bg-white/10 rounded-xl px-4 py-2 backdrop-blur-sm border border-white/20">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-white-color/90 text-sm font-medium">
            System Online
          </span>
        </div>
      </div>
    </header>
  );
};

export default Header;
