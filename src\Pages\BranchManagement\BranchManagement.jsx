import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { replacePathParams } from "../../lib/utils";
import { ROUTES } from "../../constants/route";
import {
  useDeleteBranchMutation,
  useGetBranchMutation,
} from "../../store/actions";
import { useSelector } from "react-redux";
import Loader from "../../components/custom/Loader";
import { setApiError } from "../../hooks/errorMessage";
import CustomPagination from "../../components/custom/Pagination";
import usePagination from "../../hooks/usePagination";

const BranchManagement = () => {
  const [getBranch] = useGetBranchMutation();
  const [deleteBranch, { isLoading: deleteLoader }] = useDeleteBranchMutation();

  const { data, isLoading, totalPages } = useSelector((state) => state.branch);

  const { page, setPage, limit, setLimit } = usePagination(getBranch, {
    search: "",
    status: "",
  });

  const navigate = useNavigate();

  const handleAdd = () => {
    navigate(replacePathParams(ROUTES.ADD_BRANCH_MANAGEMENT));
  };

  const handleEdit = (id) => {
    navigate(replacePathParams(ROUTES.EDIT_BRANCH_MANAGEMENT, { id: id }));
  };

  const handleDelete = (id) => {
    if (!deleteLoader) {
      deleteBranch(id).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          getBranch();
        }
      });
    }
  };

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className="p-8 flex-col flex-1 h-screen overflow-y-hidden">
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <div className="flex flex-col gap-4">
          <div className="flex flex-1 justify-end">
            <Button onClick={() => handleAdd()}>Add Branch</Button>
          </div>
          <Table>
            <TableHeader>
              <TableRow className="border-0 bg-gradient-to-r from-gray-50 to-gray-100/80 hover:from-gray-100 hover:to-gray-200/80 transition-all duration-200">
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Branch Code
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Branch Name
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Branch Address
                </TableHead>
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item?.branch_code}</TableCell>
                  <TableCell>{item?.branch_name}</TableCell>

                  <TableCell>{item?.branch_address}</TableCell>
                  {/* <TableCell>{item?.branch_manager}</TableCell> */}
                  {/* <TableCell>{item?.branch_manager_id}</TableCell> */}

                  <TableCell>
                    <div className="flex flex-row gap-1.5 items-center">
                      <Button
                        size="icon"
                        className="text-amber-400 border-amber-400 hover:border-amber-400/70 hover:text-amber-400/70"
                        variant="outline"
                        onClick={() => {
                          handleEdit(item._id);
                        }}
                      >
                        <Pencil />
                      </Button>
                      <Button
                        size="icon"
                        className="text-red-500 border-red-500 hover:border-red-500/70 hover:text-red-500/70"
                        variant="outline"
                        onClick={() => handleDelete(item._id)}
                      >
                        <Trash2 />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          <CustomPagination
            currentPage={page}
            totalPages={totalPages}
            limit={limit}
            onPageChange={setPage}
            onLimitChange={setLimit}
          />
        </div>
      </div>
    </div>
  );
};

export default BranchManagement;
