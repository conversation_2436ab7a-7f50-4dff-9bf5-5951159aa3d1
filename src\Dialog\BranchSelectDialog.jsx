/* eslint-disable react/prop-types */
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { BriefcaseBusiness } from "lucide-react";
import DateField from "@/components/custom/DateField";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useNavigate } from "react-router-dom";

const formSchema = z.object({
  closureDate: z.date().min(new Date("1900-01-01"), "Required"),
  loanType: z.string().trim().min(1, "Required"),
  branchCode: z.string().trim().min(1, "Required"),
  branchName: z.string().trim().min(1, "Required"),
  branchManagerName: z.string().trim().min(1, "Required"),
  applicantEmployeeId: z.string().trim().min(1, "Required"),
  applicantEmployeeNumber: z.string().trim().min(1, "Required"),
  applicantEmployeeEmail: z.string().trim().min(1, "Required"),
});
const BranchSelectDialog = ({ dialog, setDialog }) => {
  const navigate = useNavigate();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      closureDate: "",
      branchCode: "",
      loanType: "",
      branchName: "",
      branchManagerName: "",
      applicantEmployeeId: "",
      applicantEmployeeNumber: "",
      applicantEmployeeEmail: "",
    },
  });

  const {
    setValue: reset,
    formState: { errors },
  } = form;

  const handleSubmit = (values) => {
    sessionStorage.setItem("loanType", values.loanType);
    console.log(values);
    reset();
    setDialog(false);
  };

  const handleCancel = () => {
    reset();
    setDialog(false);
    navigate("/");
  };

  return (
    <Dialog open={dialog}>
      <DialogContent className="p-6 !max-w-3xl" isClose={false}>
        <DialogTitle>
          <h2 className="text-lg font-semibold mb-4 flex flex-row gap-1.5 items-center text-primary-color">
            <BriefcaseBusiness />
            Branch Details
          </h2>
        </DialogTitle>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <div className="flex flex-col md:flex-row gap-2.5">
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="closureDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expected Closure Date</FormLabel>
                      <DateField field={field} error={errors.closureDate} />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="loanType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Loan Type</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select Loan Type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="business">
                              Business Loan
                            </SelectItem>
                            <SelectItem value="personal">
                              Personal Loan
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="branchCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Branch Code"
                          error={errors.branchCode}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-2.5">
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="branchName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Branch Name"
                          error={errors.branchName}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="branchManagerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch Manager Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Branch Manager Name"
                          error={errors.branchManagerName}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-2.5">
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="applicantEmployeeId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Applicant Employee ID</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Applicant Employee ID"
                          error={errors.applicantEmployeeId}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="applicantEmployeeNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Applicant Employee Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Applicant Employee Number"
                          error={errors.applicantEmployeeNumber}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="applicantEmployeeEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Applicant Employee Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Applicant Employee Email"
                          error={errors.applicantEmployeeEmail}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="flex flex-row justify-center gap-2">
              <div>
                <Button type="submit" className="w-full">
                  Submit
                </Button>
              </div>
              <div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default BranchSelectDialog;
