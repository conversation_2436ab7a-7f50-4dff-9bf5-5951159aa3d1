import { z } from "zod";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import LoginImage from "../../assets/login.svg";
import { useNavigate } from "react-router-dom";
import { useLoginUserMutation } from "../../store/actions";
import { setApiError } from "../../hooks/errorMessage";
import InputField from "../../components/custom/InputField";

const formSchema = z.object({
  email: z.string().min(1).max(50),
  password: z.string().min(8).max(1000),
});

const Login = () => {
  const navigate = useNavigate();

  const [loginApi] = useLoginUserMutation();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  function onSubmit(values) {
    loginApi(values).then((res) => {
      if (res.error) {
        setApiError(res);
      } else {
        console.log("data >>> ", res?.data);
        sessionStorage.setItem("token", res?.data?.data?.token);
        sessionStorage.setItem("userEmail", res?.data?.data?.user?.email);
        sessionStorage.setItem("userRole", res?.data?.data?.user?.role);
        navigate("/");
      }
    });
  }

  return (
    <div className="flex">
      <div className="w-1/2 border-r-2">
        <div className="flex justify-center items-center h-screen p-12 flex-col gap-6">
          <div className="flex flex-col justify-center gap-4 items-center">
            <h1 className="heading-text">Sign In</h1>
            <p className="secondary-text">Sign in to stay connected.</p>
          </div>

          <div className="w-full pr-20 pl-20">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-8"
              >
                <InputField
                  control={form.control}
                  name="email"
                  placeholder="Enter Email"
                  label="Email"
                  isRequired
                />

                <InputField
                  control={form.control}
                  name="password"
                  placeholder="Enter Password"
                  label="Password"
                  type={"password"}
                  isRequired
                />

                <div className="flex items-center justify-center">
                  <Button type="submit">Submit</Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
      <div className="w-1/2">
        <div className="h-screen overflow-hidden">
          <img src={LoginImage} />
        </div>
      </div>
    </div>
  );
};

export default Login;
