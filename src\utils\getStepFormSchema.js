import { z } from "zod";

const personalInfoSchema = z
  .object({
    fname: z.string().trim().min(1, { message: "Required" }).max(50),
    mname: z.string().trim().min(1, { message: "Required" }).max(50),
    lname: z.string().trim().min(1, { message: "Required" }).max(50),
    motherName: z.string().trim().min(1, { message: "Required" }).max(50),
    dateOfBirth: z
      .date({ required_error: "Date is required" }) // ✅ Ensure error message appears
      .min(new Date("1900-01-01"), { message: "Date must be after 01/01/1900" })
      .max(new Date(), { message: "Date cannot be in the future" }),
    gender: z.string().trim().min(1, { message: "Required" }),
    maritalStatus: z.string().trim().min(1, { message: "Required" }),
    nationality: z.string().trim().min(1, { message: "Required" }),
    phoneCode: z.string().trim().min(1, { message: "Required" }),
    phoneNumber: z
      .string()
      .trim()
      .min(10, { message: "Required" })
      .max(10, { message: "Maximum 10 digits allowed" }),
    alternatePhoneCode: z.string().trim().min(1, { message: "Required" }),
    alternatePhoneNumber: z
      .string()
      .trim()
      .min(10, { message: "Required" })
      .max(10, { message: "Maximum 10 digits allowed" }),
    email: z.string().trim().email({ message: "Invalid email" }),
    loanProductId: z.string().trim().min(1, { message: "Required" }),
    occupationType: z.string().trim().min(1, { message: "Required" }),
    occupationTypeName: z.string().trim().optional(), // Initially optional, will be conditionally required
    constitution: z.string().trim().min(1, { message: "Required" }),
    businessStartDate: z
      .date({ required_error: "Business start date is required" }) // ✅ Ensure error message appears
      .min(new Date("1900-01-01"), { message: "Date must be after 01/01/1900" })
      .max(new Date(), { message: "Date cannot be in the future" }),
    financialYear: z.string().trim().min(1, { message: "Required" }),
    firstBorrowerName: z.string().trim().min(1, { message: "Required" }),
    grandTotalIncome: z.number().min(1, { message: "Required" }),
    purposeOfLoan: z.string().trim().min(1, { message: "Required" }),
    applicantExperience: z.string().trim().min(1, { message: "Required" }),
    firstBorrowerFinancialIncome: z
      .string()
      .trim()
      .min(1, { message: "Required" }),
    totalRepaymentCapacity: z.number().min(1, { message: "Required" }),
    applicantDetails: z.array(
      z.object({
        name: z.string().trim().min(1, { message: "Required" }).max(50),
        address: z.string().trim().min(1, { message: "Required" }),
        phone: z.string().trim().min(1, { message: "Required" }),
      })
    ),
    businessAddressLineFirst: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(100, { message: "Maximum 100 characters allowed" }),
    businessAddressLineSecond: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(100, { message: "Maximum 100 characters allowed" }),
    businessAddressLineThird: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(100, { message: "Maximum 100 characters allowed" }),
    businessAddressCity: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(50, { message: "Maximum 50 characters allowed" }),
    businessAddressState: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(50, { message: "Maximum 50 characters allowed" }),
    businessAddressPincode: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(8, { message: "Maximum 8 characters allowed" }),
    businessAddressCountry: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(30, { message: "Maximum 30 characters allowed" }),
    homeAddressLineFirst: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(100, { message: "Maximum 100 characters allowed" }),
    homeAddressLineSecond: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(100, { message: "Maximum 100 characters allowed" }),
    homeAddressLineThird: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(100, { message: "Maximum 100 characters allowed" }),
    homeAddressCity: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(50, { message: "Maximum 50 characters allowed" }),
    homeAddressState: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(50, { message: "Maximum 50 characters allowed" }),
    homeAddressPincode: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(8, { message: "Maximum 8 characters allowed" }),
    homeAddressCountry: z
      .string()
      .trim()
      .min(1, { message: "Required" })
      .max(30, { message: "Maximum 30 characters allowed" }),
  })
  .superRefine((data, ctx) => {
    if (data.occupationType === "other" && !data.occupationName) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["occupationTypeName"],
        message: "Occupation Name is required when Occupation Type is Other",
      });
    }
  });

const loanDetailsSchema = z.object({
  gstNumber: z.string().trim().min(1, { message: "Required" }),
  panNumber: z.string().trim().min(1, { message: "Required" }),
  businessRegistrationNumber: z.string().trim().min(1, { message: "Required" }),
  moratoriumPush: z.string().trim().min(1, { message: "Required" }),
  businessSector: z.string().trim().min(1, { message: "Required" }),
  typeOfLoan: z.string().trim().min(1, { message: "Required" }),
  loanAmount: z.string().trim().min(1, { message: "Required" }),
  interestType: z.string().trim().min(1, { message: "Required" }),
  rateOfInterest: z.string().trim().min(1, { message: "Required" }),
});

const colatrolDetailsSchema = z.object({
  propertyOwnerName: z.string().trim().min(1, { message: "Required" }),
  propertyAddress: z.string().trim().min(1, { message: "Required" }),
  propertyType: z.string().trim().min(1, { message: "Required" }),
  landArea: z.string().trim().min(1, { message: "Required" }),
  constructionArea: z.string().trim().min(1, { message: "Required" }),
  propertyValuation: z.string().trim().min(1, { message: "Required" }),
  valuationDate: z.coerce.date(),
});

const documentDetailsSchema = z.object({
  documents: z.array(
    z.object({
      documentName: z.string().nonempty("Document name is required"),
      value: z.enum(["Yes", "No", "Maybe"], {
        required_error: "Selection is required",
      }),
    })
  ),
});

export const getSchemaForStep = (step) => {
  switch (step) {
    case 0:
      return personalInfoSchema;
    case 1:
      return loanDetailsSchema;

    case 2:
      return colatrolDetailsSchema;

    case 3:
      return documentDetailsSchema;
    // Add more cases for other steps
    default:
      return z.object({});
  }
};
