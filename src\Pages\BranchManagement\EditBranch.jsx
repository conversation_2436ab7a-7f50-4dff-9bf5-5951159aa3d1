import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { setApiError } from "../../hooks/errorMessage";
import { useNavigate, useParams } from "react-router-dom";
import { z } from "zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  useAddBranchMutation,
  useGetSingleBranchMutation,
} from "../../store/actions";
import { ROUTES } from "../../constants/route";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { usePrefillFormFields } from "../../hooks/useAutoFillForm";

const formSchema = z.object({
  branchName: z.string().min(1).max(50),
  branchCode: z.string().min(1).max(8),
  branchAddress: z.string().min(1),
  branchManager: z.string().min(1).max(40),
  branchManagerId: z.string().min(1).max(8),
  branchStatus: z.string().optional(),
});

const EditBranch = () => {
  const navigate = useNavigate();

  const { id } = useParams();

  const [addBranch, { isLoading: addBranchLoader }] = useAddBranchMutation();
  const [getBranch] = useGetSingleBranchMutation();

  useEffect(() => {
    getBranch(id);
  }, []);

  const { branch, isLoading } = useSelector((state) => state.branch);

  console.log("branch >> ", [branch]);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      branchName: "",
      branchCode: "",
      branchAddress: "",
      branchManager: "",
      branchManagerId: "",
    },
  });

  usePrefillFormFields({
    data: branch,
    methods: form,
    keys: {
      branchName: "branch_name",
      branchCode: "branch_code",
      branchAddress: "branch_address",
      branchManager: "branch_manager",
      branchManagerId: "branch_manager_id",
    },
  });

  function onSubmit(values) {
    const finalValues = {
      branch_name: values.branchName,
      branch_code: values.branchCode,
      branch_address: values.branchAddress,
      branch_manager: values.branchManager,
      branch_manager_id: values.branchManagerId,
      branch_status: "pending",
    };
    if (!addBranchLoader) {
      addBranch(finalValues).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          navigate(ROUTES.BRANCH_MANAGEMENT);
        }
      });
    }
  }

  return (
    <div className="p-8 flex-col flex-1 h-screen overflow-y-hidden">
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-6 pb-2">
              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="branchName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Branch Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter Branch Name" {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="branchCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Branch Code</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter Branch Code" {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="branchAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Branch Address</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter Branch Address"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="branchManager"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Branch Manager Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter Branch Manager Name"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="branchManagerId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Branch Manager Id</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter Branch Manager Id"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex items-center justify-center">
                <Button type="submit">Submit</Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default EditBranch;
