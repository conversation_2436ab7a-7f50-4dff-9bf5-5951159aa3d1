import { useEffect, useState } from "react";

const usePagination = (fetchData, defaultFilters = {}) => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [filters, setFilters] = useState(defaultFilters);

  useEffect(() => {
    fetchData({ page, limit, ...filters });
  }, [page, limit, filters]);

  const updateFilters = (newFilters) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
    setPage(1); // Reset to page 1 when filters change
  };

  return { page, setPage, limit, setLimit, filters, updateFilters };
};

export default usePagination;
