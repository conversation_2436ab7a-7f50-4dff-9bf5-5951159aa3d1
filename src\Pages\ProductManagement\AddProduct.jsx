import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { setApiError } from "../../hooks/errorMessage";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import {
  useAddProductMutation,
  useGetLoanTypeMutation,
} from "../../store/actions";
import { ROUTES } from "../../constants/route";
import InputField from "../../components/custom/InputField";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import FormSelect from "../../components/custom/SelectInput";
import RadioButton from "../../components/custom/RadioButton";

const formSchema = z.object({
  loanTypeId: z.string().min(1),
  productCode: z.string().min(1).max(4),
  minAmount: z.string().min(1),
  maxAmount: z.string().min(1),
  timePeriod: z.string().min(1),
  interestRate: z.string().min(1),
  isJoint: z.boolean(),
  productStatus: z.string().optional(),
});

const AddProduct = () => {
  const navigate = useNavigate();

  const [addProduct, { isLoading: addProductLoader }] = useAddProductMutation();

  const [getLoanTypes] = useGetLoanTypeMutation();

  useEffect(() => {
    getLoanTypes();
  }, []);

  const { loanTypes, isLoading } = useSelector(
    (state) => state.primaryApplication
  );

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      loanTypeId: "",
      productCode: "",
      minAmount: "",
      maxAmount: "",
      timePeriod: "",
      interestRate: "",
      isJoint: true,
      productStatus: "active",
    },
  });

  function onSubmit(values) {
    console.log(values);
    if (!addProductLoader) {
      addProduct(values).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          navigate(ROUTES.PRODUCT_MANAGEMENT);
        }
      });
    }
  }

  return (
    <div className="p-8 flex-col flex-1 h-screen overflow-y-hidden">
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-6 pb-2">
              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <FormSelect
                    label="Loan Type"
                    name="loanTypeId"
                    isRequired
                    control={form.control}
                    disabled={isLoading}
                    placeholder="Select Loan Type"
                    options={loanTypes}
                  />
                </div>
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="productCode"
                    label="Product Code"
                    placeholder="Enter Product Code"
                    isRequired
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="minAmount"
                    label="Minimum Product Amount"
                    placeholder="Enter Minimum Product Amount"
                    isRequired
                  />
                </div>
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="maxAmount"
                    label="Maximum Product Amount"
                    placeholder="Enter Maximum Product Amount"
                    isRequired
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="timePeriod"
                    label="Product Time Period (Years)"
                    placeholder="Enter Product Time Period"
                    isRequired
                  />
                </div>
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="interestRate"
                    label="Interest Rate"
                    placeholder="Enter Interest Rate"
                    isRequired
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <RadioButton
                    name="isJoint"
                    label="Joint Loan Applicable or not?"
                    formControl={form.control}
                    isRequired
                    options={[
                      { label: "Yes", value: true },
                      { label: "No", value: false },
                    ]}
                  />
                </div>
              </div>

              <div className="flex items-center justify-center">
                <Button type="submit">Submit</Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default AddProduct;
