import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { useSelector } from "react-redux";

import { ROUTES } from "../../constants/route";
import {
  useDeleteProductMutation,
  useGetProductMutation,
} from "../../store/actions";
import usePagination from "../../hooks/usePagination";
import { replacePathParams } from "../../lib/utils";
import { setApiError } from "../../hooks/errorMessage";
import Loader from "../../components/custom/Loader";
import CustomPagination from "../../components/custom/Pagination";

const ProductManagement = () => {
  const [getProduct] = useGetProductMutation();
  const [deleteProduct, { isLoading: deleteLoader }] =
    useDeleteProductMutation();

  const { data, isLoading, totalPages } = useSelector((state) => state.product);

  const { page, setPage, limit, setLimit } = usePagination(getProduct, {
    search: "",
    status: "",
  });

  const navigate = useNavigate();

  const handleAdd = () => {
    navigate(replacePathParams(ROUTES.ADD_PRODUCT_MANAGEMENT));
  };

  const handleEdit = (id) => {
    navigate(replacePathParams(ROUTES.EDIT_PRODUCT_MANAGEMENT, { id: id }));
  };

  const handleDelete = (id) => {
    if (!deleteLoader) {
      deleteProduct(id).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          getProduct();
        }
      });
    }
  };

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className="p-8 flex-col flex-1 h-screen overflow-y-hidden">
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <div className="flex flex-col gap-4">
          <div className="flex flex-1 justify-end">
            <Button onClick={() => handleAdd()}>Add Product</Button>
          </div>
          <Table>
            <TableHeader>
              <TableRow className="border-0 bg-gradient-to-r from-gray-50 to-gray-100/80 hover:from-gray-100 hover:to-gray-200/80 transition-all duration-200">
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Product Code
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Product Name
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Minimum Amount
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Maximum Amount
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Joint
                </TableHead>
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item?.productCode}</TableCell>
                  <TableCell>{item?.loanTypeId?.name}</TableCell>

                  <TableCell>{item?.minAmount}</TableCell>
                  <TableCell>{item?.maxAmount}</TableCell>
                  <TableCell>{item?.isJoint ? "Yes" : "No"}</TableCell>

                  <TableCell>
                    <div className="flex flex-row gap-1.5 items-center">
                      <Button
                        size="icon"
                        className="text-amber-400 border-amber-400 hover:border-amber-400/70 hover:text-amber-400/70"
                        variant="outline"
                        onClick={() => {
                          handleEdit(item._id);
                        }}
                      >
                        <Pencil />
                      </Button>
                      <Button
                        size="icon"
                        className="text-red-500 border-red-500 hover:border-red-500/70 hover:text-red-500/70"
                        variant="outline"
                        onClick={() => handleDelete(item._id)}
                      >
                        <Trash2 />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          <CustomPagination
            currentPage={page}
            totalPages={totalPages}
            limit={limit}
            onPageChange={setPage}
            onLimitChange={setLimit}
          />
        </div>
      </div>
    </div>
  );
};

export default ProductManagement;
