/* eslint-disable react/prop-types */
import { Form<PERSON>rovider, useForm } from "react-hook-form";
import { useEffect } from "react";
import FieldRenderer from "@/components/FormRenderer/FieldRenderer";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Users,
  Send,
  User,
  Home,
  CreditCard,
  Shield,
  DollarSign,
  Building,
  CheckCircle,
  UserCheck,
  Briefcase,
  GraduationCap,
  BookOpen,
  BarChart3,
  MapPin,
  FileCheck,
} from "lucide-react";

// Icon mapping for dynamic icon rendering
const iconMap = {
  FileText,
  Users,
  Send,
  User,
  Home,
  CreditCard,
  Shield,
  DollarSign,
  Building,
  CheckCircle,
  UserCheck,
  Briefcase,
  GraduationCap,
  BookOpen,
  BarChart3,
  MapPin,
  FileCheck,
};

export default function FormRenderer({
  schema,
  defaultValues,
  onSubmit,
  fieldOptions,
  onAddApplicant,
  isAdditional,
  showHeader = true,
  showActions = true,
  formMethodsRef,
}) {
  const methods = useForm({
    defaultValues,
    mode: "onBlur",
  });

  const {
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = methods;

  // Set the form methods ref for external access (e.g., auto-population)
  useEffect(() => {
    if (formMethodsRef) {
      formMethodsRef.current = methods;
    }
  }, [formMethodsRef, methods]);

  // Reset form when defaultValues change
  useEffect(() => {
    if (defaultValues && Object.keys(defaultValues).length > 0) {
      console.log(
        "FormRenderer: Resetting form with new defaultValues:",
        defaultValues
      );
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const handleAdd = handleSubmit((data) => {
    onAddApplicant(data, reset); // pass reset so next applicant can be filled
  });

  const handleFinalSubmit = handleSubmit(onSubmit);

  // Get error count for validation feedback
  const errorCount = Object.keys(errors).length;

  return (
    <FormProvider {...methods}>
      <div className="w-full min-h-screen bg-gray-50">
        <div className="w-full max-w-none mx-auto px-4 py-6">
          {/* Schema Header */}
          {showHeader && (
            <div className="mb-8">
              <Card className="border-l-4 border-l-blue-500 shadow-lg">
                <CardHeader className="pb-6 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-white rounded-xl shadow-md">
                        <FileText className="h-8 w-8 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle className="text-3xl font-bold text-gray-900 mb-2">
                          {schema.title || `${schema.loanCode} Application`}
                        </CardTitle>
                        <CardDescription className="text-gray-700 text-lg">
                          {schema.description ||
                            "Complete all sections to submit your application"}
                        </CardDescription>
                        {schema.subtitle && (
                          <p className="text-sm text-gray-600 mt-2">
                            {schema.subtitle}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <Badge
                        variant="outline"
                        className="text-sm font-semibold px-3 py-1"
                      >
                        {schema.loanCode}
                      </Badge>
                      {schema.estimatedTime && (
                        <span className="text-xs text-gray-500">
                          Est. time: {schema.estimatedTime}
                        </span>
                      )}
                    </div>
                  </div>
                </CardHeader>
              </Card>
            </div>
          )}

          <form onSubmit={handleFinalSubmit} className="space-y-8">
            {schema.sections.map((section, sectionIndex) => {
              // ✅ Filter out hidden fields
              const visibleFields = section.fields.filter((field) => {
                if (field.onlyForAdditionalApplicant && !isAdditional)
                  return false;
                return true;
              });

              // ❌ Skip section if all fields are hidden
              if (visibleFields.length === 0) return null;

              return (
                <div key={section.sectionId} className="mb-12">
                  <div
                    className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 pb-8 rounded-2xl mb-6 p-8 relative overflow-hidden"
                    style={{
                      border: "none !important",
                      boxShadow: "none !important",
                      outline: "none",
                    }}
                  >
                    {/* Decorative background elements */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full -translate-y-16 translate-x-16"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-200/30 to-blue-200/30 rounded-full translate-y-12 -translate-x-12"></div>
                    <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                      <div className="flex items-center gap-4">
                        {section.iconName && iconMap[section.iconName] && (
                          <div
                            className="p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg relative z-10"
                            style={{ border: "none !important" }}
                          >
                            {(() => {
                              const IconComponent = iconMap[section.iconName];
                              return (
                                <IconComponent
                                  className={`h-8 w-8 ${
                                    section.iconColor || "text-blue-600"
                                  }`}
                                />
                              );
                            })()}
                          </div>
                        )}
                        <div className="flex-1">
                          <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-3 mb-2">
                            <span className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-bold px-3 py-1 rounded-full shadow-sm">
                              {sectionIndex + 1}
                            </span>
                            {section.sectionTitle}
                          </CardTitle>
                          {section.description && (
                            <CardDescription className="text-gray-600 text-base">
                              {section.description}
                            </CardDescription>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {section.required && (
                          <Badge
                            variant="destructive"
                            className="text-xs font-medium px-3 py-1 bg-red-100 text-red-800 border border-red-200"
                          >
                            Required
                          </Badge>
                        )}
                        {section.adminOnly && (
                          <Badge
                            variant="secondary"
                            className="text-xs font-medium px-3 py-1 bg-gray-100 text-gray-800 border border-gray-200"
                          >
                            Admin Only
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div
                    className="p-8 bg-white rounded-lg mt-4"
                    style={{
                      border: "none",
                      boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
                    }}
                  >
                    <div
                      className={`grid gap-6 ${
                        section.gridClass ||
                        "grid-cols-1 lg:grid-cols-2 xl:grid-cols-3"
                      }`}
                    >
                      {visibleFields.map((field) => (
                        <div
                          key={field.name}
                          className={`w-full ${
                            field.colSpan === 2
                              ? "lg:col-span-2 xl:col-span-2"
                              : field.colSpan === 3
                              ? "lg:col-span-3 xl:col-span-3"
                              : field.colSpan === "full"
                              ? "col-span-full"
                              : ""
                          }`}
                        >
                          <FieldRenderer
                            field={field}
                            control={control}
                            fieldOptions={fieldOptions}
                            colSpan={field.colSpan || 1}
                            isAdditional={isAdditional}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Form Actions */}
            {showActions && (
              <div className="mt-8 bg-white rounded-xl shadow-sm border-0">
                <div className="p-6">
                  <div className="flex flex-col lg:flex-row gap-6 justify-between items-center">
                    <div className="flex items-center gap-4">
                      {errorCount > 0 ? (
                        <div className="flex items-center gap-3 px-4 py-2 bg-red-50 rounded-lg border-l-4 border-red-400">
                          <div className="flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          </div>
                          <span className="font-medium text-red-700 text-base">
                            {errorCount} field{errorCount > 1 ? "s" : ""} need
                            {errorCount === 1 ? "s" : ""} attention
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-3 px-4 py-2 bg-green-50 rounded-lg border-l-4 border-green-400">
                          <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          </div>
                          <span className="font-medium text-green-700 text-base">
                            All fields are valid
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex gap-4">
                      {onAddApplicant && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleAdd}
                          className="flex items-center gap-2 px-6 py-3 text-base font-medium border-2 hover:bg-gray-50"
                        >
                          <Users className="h-5 w-5" />
                          Add Applicant
                        </Button>
                      )}
                      <Button
                        type="submit"
                        className="flex items-center gap-2 px-8 py-3 text-base font-semibold bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 shadow-lg"
                      >
                        <Send className="h-5 w-5" />
                        Submit Application
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </form>
        </div>
      </div>
    </FormProvider>
  );
}
