/* eslint-disable react/prop-types */
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import InputField from "../components/custom/InputField";

const PersonalInfoDialog = ({ dialog, setDialog, form, handleSubmit }) => {
  const handleFormSubmit = () => {
    // Manually trigger the form submit
    form.handleSubmit(handleSubmit)();
  };
  return (
    <Dialog open={dialog} onOpenChange={setDialog}>
      <DialogContent className="p-6">
        <DialogTitle>
          <h2 className="text-lg font-semibold mb-4">Add Personal Info</h2>
        </DialogTitle>
        <Form {...form}>
          <form className="space-y-4">
            <InputField
              control={form.control}
              name="name"
              label="Name"
              placeholder="Enter name"
              isRequired
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter Address"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <InputField
              control={form.control}
              name="phone"
              label="Phone"
              placeholder="Enter Phone number"
              type="number"
              isRequired
            />

            <Button type="button" className="w-full" onClick={handleFormSubmit}>
              Submit
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default PersonalInfoDialog;
