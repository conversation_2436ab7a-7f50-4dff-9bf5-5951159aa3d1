"use client";

import { useEffect } from "react";

// Helper function to get nested value safely
function getNestedValue(obj, path) {
  return path.split(".").reduce((acc, part) => acc?.[part], obj);
}

export function usePrefillFormFields({ data, param, methods, keys }) {
  useEffect(() => {
    if (param !== "new" && data && Object.keys(data).length > 0) {
      const values = {};

      Object.entries(keys).forEach(([formKey, dataKey]) => {
        const value = getNestedValue(data, dataKey);
        if (value !== undefined) {
          values[formKey] = value;
        }
      });

      methods.reset({
        ...methods.getValues(),
        ...values,
      });
    }
  }, [JSON.stringify(data)]);
}
