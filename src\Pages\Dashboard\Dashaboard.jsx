import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Overview } from "./Overview";
import { RecentSales } from "./RecentSales";

export default function DashboardPage() {
  return (
    <>
      <div className="flex-col md:flex flex-1 relative">
        <div className="space-y-6 p-6 pt-4">
          {/* Welcome Section */}
          <div className="mb-8">
            <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 shadow-sm">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                Dashboard Overview
              </h2>
              <p className="text-gray-600">
                Monitor your loan management system performance and key metrics.
              </p>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-semibold text-gray-700">
                  Total Revenue
                </CardTitle>
                <div className="p-2 bg-green-100 rounded-xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5 text-green-600"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-3xl font-bold text-gray-800 mb-1">
                  $45,231.89
                </div>
                <p className="text-sm text-green-600 font-medium flex items-center gap-1">
                  <span className="text-green-500">↗</span>
                  +20.1% from last month
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-semibold text-gray-700">
                  Active Loans
                </CardTitle>
                <div className="p-2 bg-blue-100 rounded-xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5 text-blue-600"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-3xl font-bold text-gray-800 mb-1">
                  2,350
                </div>
                <p className="text-sm text-blue-600 font-medium flex items-center gap-1">
                  <span className="text-blue-500">↗</span>
                  +180.1% from last month
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-semibold text-gray-700">
                  Applications
                </CardTitle>
                <div className="p-2 bg-purple-100 rounded-xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5 text-purple-600"
                  >
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <path d="M2 10h20" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-3xl font-bold text-gray-800 mb-1">
                  12,234
                </div>
                <p className="text-sm text-purple-600 font-medium flex items-center gap-1">
                  <span className="text-purple-500">↗</span>
                  +19% from last month
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 rounded-2xl overflow-hidden relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-semibold text-gray-700">
                  Active Users
                </CardTitle>
                <div className="p-2 bg-orange-100 rounded-xl">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-5 w-5 text-orange-600"
                  >
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                  </svg>
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-3xl font-bold text-gray-800 mb-1">573</div>
                <p className="text-sm text-orange-600 font-medium flex items-center gap-1">
                  <span className="text-orange-500">↗</span>
                  +201 since last hour
                </p>
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4 bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-lg rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-gray-50/50 to-white/50 border-b border-gray-200/50">
                <CardTitle className="text-xl font-bold text-gray-800">
                  Performance Overview
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Monthly performance metrics and trends
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2 pt-6">
                <Overview />
              </CardContent>
            </Card>
            <Card className="col-span-3 bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-lg rounded-2xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-gray-50/50 to-white/50 border-b border-gray-200/50">
                <CardTitle className="text-xl font-bold text-gray-800">
                  Recent Activity
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Latest loan applications and approvals
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <RecentSales />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
