// import {
//     useAddHousingApplicantMutation,
//     useSubmitHousingLoanMutation,
// } from "@/store/apis/housingLoanApi";
// import {
//     useAddEducationApplicantMutation,
//     useSubmitEducationLoanMutation,
// } from "@/store/apis/educationLoanApi";

// export const loanTypeHandlers = {
//     HL: {
//         useAddApplicant: useAddHousingApplicantMutation,
//         useSubmitLoan: useSubmitHousingLoanMutation,
//     },
//     EL: {
//         useAddApplicant: useAddEducationApplicantMutation,
//         useSubmitLoan: useSubmitEducationLoanMutation,
//     },
// // Add more loan types here
//   };