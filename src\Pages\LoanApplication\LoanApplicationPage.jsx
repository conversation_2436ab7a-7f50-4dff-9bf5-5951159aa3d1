import { useParams, useNavigate } from "react-router-dom";
import FormRenderer from "@/components/FormRenderer/FormRenderer";
import { useEffect, useState, useRef } from "react";
import { toast } from "sonner";
import { useGetPrimaryApplicationMutation } from "@/store/actions/primaryApplication/primaryApplication";
import {
  mapPrimaryApplicationToForm,
  debugPrimaryApplicationData,
} from "@/utils/dataMapper.js";
import { schemaMap, getSchemaMetadata } from "@/schemas";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CardTitle } from "@/components/ui/card";
import { ArrowLeft, FileText } from "lucide-react";
import { ROUTES } from "@/constants/route";
// import { loanTypeHandlers } from "@/config/loanTypeHandlers";

// Import test utilities for debugging
import "@/utils/dataMapper.test.js";

export default function LoanApplicationPage() {
  const { loanCode, primaryAppId } = useParams();
  const navigate = useNavigate();

  const [getPrimaryApplicationData, { isLoading }] =
    useGetPrimaryApplicationMutation();

  const [formDefaultValues, setFormDefaultValues] = useState({});
  const [applicants, setApplicants] = useState([]);
  const [isAdditionalApplicant, setIsAdditionalApplicant] = useState(false);

  const selectedSchema = schemaMap[loanCode];
  const schemaMetadata = getSchemaMetadata(loanCode);
  const formMethodsRef = useRef(null);

  // const loanHandlers = loanTypeHandlers[loanCode];

  // if (!loanHandlers) {
  //   return <p className="text-red-600">Unsupported loan type: {loanCode}</p>;
  // }

  // const [addApplicantApi] = loanHandlers.useAddApplicant();
  // const [submitLoanApi] = loanHandlers.useSubmitLoan();

  useEffect(() => {
    getPrimaryApplicationData(primaryAppId).then((res) => {
      console.log("Raw API Response:", res);
      const data = res?.data?.data;
      console.log("Extracted data:", data);

      // Debug the data structure
      debugPrimaryApplicationData(data);

      if (data) {
        // Use enhanced data mapper with schema support
        const values = mapPrimaryApplicationToForm(data, selectedSchema, {
          includeEmptyFields: true,
          validateData: false,
          customMappings: {
            // Add any custom field mappings specific to this loan type
            applicationDate: new Date().toISOString().slice(0, 10),
          },
        });

        console.log("Mapped form values:", values);
        console.log("Full Name mapping:", values.fullName);
        console.log("Contact Info mapping:", values.contactInfo);
        console.log("Customer Number:", values.customerNumber);
        console.log("Gender:", values.gender);
        setFormDefaultValues(values);

        // Reset the form with the new values if form is already initialized
        if (formMethodsRef.current) {
          formMethodsRef.current.reset(values);
        }

        // Show success message with mapped field count
        const mappedFieldCount = Object.keys(values).filter((key) => {
          const value = values[key];
          // Handle nested objects
          if (
            typeof value === "object" &&
            value !== null &&
            !Array.isArray(value)
          ) {
            return Object.values(value).some(
              (v) => v !== "" && v !== null && v !== undefined
            );
          }
          return value !== "" && value !== null && value !== undefined;
        }).length;

        if (mappedFieldCount > 0) {
          toast.success(
            `Auto-filled ${mappedFieldCount} fields from primary application`
          );
        }
      }
    });
  }, [selectedSchema, getPrimaryApplicationData, primaryAppId]);

  // Monitor form default values changes
  useEffect(() => {
    console.log("Form default values updated:", formDefaultValues);
  }, [formDefaultValues]);

  if (!selectedSchema) {
    return <p className="text-red-600">Invalid or missing loan schema.</p>;
  }

  if (isLoading) return <p>Loading form...</p>;

  const handleAddApplicant = async (formData, reset) => {
    setApplicants((prev) => [...prev, formData]);
    toast.success("Applicant added");
    reset(formDefaultValues);
    setIsAdditionalApplicant(true);
  };

  // const handleAddApplicant = async (formData, reset) => {
  //   try {
  //     const res = await addApplicantApi(formData).unwrap();
  //     const { applicantId } = res;

  //     setApplicants((prev) => [...prev, { ...formData, applicantId }]);
  //     toast.success("Applicant added");
  //     reset(formDefaultValues);
  //     setIsAdditionalApplicant(true);
  //   } catch (err) {
  //     toast.error("Failed to add applicant");
  //     console.error(err);
  //   }
  // };

  const handleSubmit = async (formData) => {
    const finalList = applicants.length ? applicants : [{ ...formData }];

    const payload = {
      loanCode,
      primaryApplicationId: primaryAppId,
      applicants: finalList,
    };

    toast.success("Loan Application Submitted!");
    console.log("Submitted Payload", payload);
    // navigate(`/loan-applications/${loanApplicationId}`);
  };

  // const handleSubmit = async (formData) => {
  //   try {
  //     const finalList = applicants.length ? applicants : [{ ...formData }];
  //     const payload = {
  //       loanCode,
  //       primaryApplicationId: primaryAppId,
  //       applicants: finalList,
  //     };

  //     const res = await submitLoanApi(payload).unwrap();

  //     toast.success("Loan Application Submitted!");
  //     console.log("Submitted Payload", payload);
  //     // navigate(`/loan-applications/${res.loanApplicationId}`);
  //   } catch (err) {
  //     toast.error("Submission failed");
  //     console.error(err);
  //   }
  // };

  const handleBackToApplications = () => {
    navigate(ROUTES.PRIMARY_APPLICATION_LIST);
  };

  return (
    <div className="p-8 flex-col flex-1 min-h-screen overflow-y-auto">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl mb-6 border border-gray-200/50 shadow-lg">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex flex-col lg:flex-row lg:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-50 rounded-xl">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-gray-900 mb-1">
                  {schemaMetadata?.title || `${loanCode} Application`}
                </CardTitle>
                <p className="text-gray-600 text-sm">
                  {schemaMetadata?.description ||
                    "Complete your loan application with ease"}
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2 text-green-600">
            <div className="flex items-center gap-3 mt-2">
              <Badge variant="secondary" className="text-xs font-medium">
                {loanCode}
              </Badge>
              <span className="text-xs text-gray-500">
                Application ID: {primaryAppId}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <FormRenderer
          schema={selectedSchema}
          defaultValues={formDefaultValues}
          onAddApplicant={handleAddApplicant}
          onSubmit={handleSubmit}
          isAdditional={isAdditionalApplicant}
          showHeader={false} // We're showing our own header
          formMethodsRef={formMethodsRef}
        />
      </div>
    </div>
  );
}
