import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { SidebarComponent } from "../Sidebar/Sidebar";
import { Routes, Route, useLocation, useNavigate } from "react-router-dom";
import Login from "../Login/Login";
import Header from "./Header";
import { Toaster } from "sonner";
import { useEffect } from "react";
import Routing from "./Routing";

const Home = () => {
  const location = useLocation();
  const isLoginPage = location.pathname === "/login";

  const navigate = useNavigate();

  const checkIsLogin = () => {
    const token = sessionStorage.getItem("token");

    if (!token) {
      navigate("/login");
    }
  };

  useEffect(() => {
    checkIsLogin();
  }, []);

  return (
    <>
      <Toaster position="top-right" />

      {!isLoginPage && (
        <SidebarProvider>
          <SidebarComponent />
          <SidebarInset className="bg-gradient-to-br from-gray-50 via-white to-gray-100/50 min-h-screen relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.03),transparent_50%)] pointer-events-none" />
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-primary-color/5 to-transparent rounded-full -translate-y-48 translate-x-48" />
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-sidebar-active-color/5 to-transparent rounded-full translate-y-40 -translate-x-40" />

            <div className="relative z-10">
              <Header />
              <Routing />
            </div>
          </SidebarInset>
        </SidebarProvider>
      )}

      {isLoginPage && (
        <Routes>
          <Route path="/login" element={<Login />} />
        </Routes>
      )}
    </>
  );
};

export default Home;
