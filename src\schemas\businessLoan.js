const businessLoanSchema = {
  loanCode: "BL",
  title: "Business Loan Application",
  description: "Apply for a business loan to grow your enterprise and achieve your business goals",
  subtitle: "Flexible financing solutions for your business needs",
  category: "Business Finance",
  estimatedTime: "20-25 minutes",
  sections: [
    {
      sectionId: "business_info",
      sectionTitle: "Business Information",
      description: "Details about your business entity and operations",
      iconName: "Building",
      iconColor: "text-blue-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "businessName",
          label: "Business/Company Name",
          type: "text",
          required: true,
          placeholder: "Enter registered business name",
          helpText: "Legal name as per registration documents"
        },
        {
          name: "businessType",
          label: "Business Type",
          type: "select",
          required: true,
          options: [
            { value: "proprietorship", label: "Sole Proprietorship" },
            { value: "partnership", label: "Partnership" },
            { value: "llp", label: "Limited Liability Partnership (LLP)" },
            { value: "private_limited", label: "Private Limited Company" },
            { value: "public_limited", label: "Public Limited Company" }
          ]
        },
        {
          name: "industryType",
          label: "Industry/Sector",
          type: "select",
          required: true,
          options: [
            { value: "manufacturing", label: "Manufacturing" },
            { value: "trading", label: "Trading" },
            { value: "services", label: "Services" },
            { value: "retail", label: "Retail" },
            { value: "technology", label: "Technology" },
            { value: "healthcare", label: "Healthcare" },
            { value: "education", label: "Education" },
            { value: "agriculture", label: "Agriculture" },
            { value: "other", label: "Other" }
          ]
        },
        {
          name: "yearOfEstablishment",
          label: "Year of Establishment",
          type: "number",
          required: true,
          min: 1900,
          max: new Date().getFullYear(),
          placeholder: "2020"
        },
        {
          name: "registrationNumber",
          label: "Business Registration Number",
          type: "text",
          required: true,
          placeholder: "Enter registration/incorporation number"
        },
        {
          name: "panNumber",
          label: "Business PAN Number",
          type: "text",
          required: true,
          placeholder: "**********",
          pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}"
        },
        {
          name: "gstNumber",
          label: "GST Registration Number",
          type: "text",
          placeholder: "Enter GST number if applicable",
          helpText: "Required for businesses with turnover > ₹20 lakhs"
        },
        {
          name: "businessAddress",
          label: "Business Address",
          type: "textarea",
          required: true,
          colSpan: 2,
          placeholder: "Enter complete business address",
          rows: 3
        }
      ]
    },
    {
      sectionId: "proprietor_details",
      sectionTitle: "Proprietor/Director Details",
      description: "Personal information of business owner(s) or key personnel",
      iconName: "User",
      iconColor: "text-purple-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "proprietorName",
          label: "Proprietor/Director Name",
          type: "text",
          required: true,
          placeholder: "Enter full name"
        },
        {
          name: "designation",
          label: "Designation",
          type: "text",
          required: true,
          placeholder: "Proprietor/Director/Managing Director"
        },
        {
          name: "personalPan",
          label: "Personal PAN Number",
          type: "text",
          required: true,
          placeholder: "**********",
          pattern: "[A-Z]{5}[0-9]{4}[A-Z]{1}"
        },
        {
          name: "aadhaarNumber",
          label: "Aadhaar Number",
          type: "text",
          required: true,
          placeholder: "1234 5678 9012"
        },
        {
          name: "contactInfo",
          label: "Contact Information",
          type: "group",
          gridClass: "grid-cols-2",
          colSpan: 2,
          fields: [
            {
              name: "phone",
              label: "Mobile Number",
              type: "tel",
              required: true,
              placeholder: "9876543210"
            },
            {
              name: "email",
              label: "Email Address",
              type: "email",
              required: true,
              placeholder: "<EMAIL>"
            }
          ]
        }
      ]
    },
    {
      sectionId: "financial_details",
      sectionTitle: "Financial Information",
      description: "Business financial performance and requirements",
      iconName: "BarChart3",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "annualTurnover",
          label: "Annual Turnover (₹)",
          type: "number",
          required: true,
          placeholder: "5000000",
          helpText: "Last financial year's turnover"
        },
        {
          name: "monthlyRevenue",
          label: "Average Monthly Revenue (₹)",
          type: "number",
          required: true,
          placeholder: "400000"
        },
        {
          name: "netProfit",
          label: "Annual Net Profit (₹)",
          type: "number",
          required: true,
          placeholder: "500000"
        },
        {
          name: "existingLoans",
          label: "Existing Business Loans Outstanding (₹)",
          type: "number",
          placeholder: "0",
          helpText: "Total outstanding amount of existing business loans"
        }
      ]
    },
    {
      sectionId: "loan_requirements",
      sectionTitle: "Loan Requirements",
      description: "Specify your loan needs and purpose",
      iconName: "DollarSign",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "loanAmount",
          label: "Required Loan Amount (₹)",
          type: "number",
          required: true,
          placeholder: "2000000",
          min: 100000,
          max: 50000000
        },
        {
          name: "loanPurpose",
          label: "Purpose of Loan",
          type: "select",
          required: true,
          options: [
            { value: "working_capital", label: "Working Capital" },
            { value: "equipment_purchase", label: "Equipment Purchase" },
            { value: "business_expansion", label: "Business Expansion" },
            { value: "inventory", label: "Inventory Purchase" },
            { value: "infrastructure", label: "Infrastructure Development" },
            { value: "technology_upgrade", label: "Technology Upgrade" },
            { value: "other", label: "Other" }
          ]
        },
        {
          name: "repaymentTenure",
          label: "Preferred Repayment Tenure (Months)",
          type: "number",
          required: true,
          min: 12,
          max: 84,
          placeholder: "36",
          helpText: "Loan tenure between 12 to 84 months"
        },
        {
          name: "collateralOffered",
          label: "Collateral/Security Offered",
          type: "textarea",
          placeholder: "Describe any collateral or security you can offer",
          helpText: "Property, equipment, or other assets as security",
          rows: 3
        }
      ]
    },
    {
      sectionId: "declaration",
      sectionTitle: "Declaration and Undertaking",
      description: "Legal declarations and commitments",
      iconName: "CheckCircle",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "informationAccuracy",
          label: "I/We declare that all business and financial information provided is true and accurate",
          type: "checkbox",
          required: true
        },
        {
          name: "financialStatements",
          label: "I/We agree to provide audited financial statements and other required documents",
          type: "checkbox",
          required: true
        },
        {
          name: "termsAcceptance",
          label: "I/We accept the bank's terms and conditions for business loans",
          type: "checkbox",
          required: true
        },
        {
          name: "repaymentCommitment",
          label: "I/We commit to repay the loan as per the agreed schedule",
          type: "checkbox",
          required: true
        },
        {
          name: "businessContinuity",
          label: "I/We undertake to maintain the business operations during the loan tenure",
          type: "checkbox",
          required: true
        }
      ]
    }
  ]
};

export default businessLoanSchema;
