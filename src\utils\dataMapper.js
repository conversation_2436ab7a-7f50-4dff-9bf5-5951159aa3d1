/**
 * Universal data mapper for form schemas
 * Maps primary application data to any form schema structure
 * @param {Object} primaryApp - Primary application data
 * @param {Object} schema - Form schema to map data to
 * @returns {Object} Mapped form values
 */
export const parsePrimaryDataToFormValues = (primaryApp, schema = null) => {
    if (!primaryApp) return {};

    // Handle different data structures - sometimes data comes wrapped, sometimes direct
    const appData = primaryApp.primaryApplication || primaryApp;

    // Handle both nested and flat data structures
    const personal = appData?.personalInfo || appData || {};
    const phone = appData?.phone || { code: appData?.phoneCode, number: appData?.phoneNumber };
    const alternatePhone = appData?.alternatePhone || { code: appData?.alternatePhoneCode, number: appData?.alternatePhoneNumber };
    const businessAddress = appData?.businessAddress || {
        line1: appData?.businessAddressLineFirst,
        line2: appData?.businessAddressLineSecond,
        line3: appData?.businessAddressLineThird,
        city: appData?.businessAddressCity,
        state: appData?.businessAddressState,
        pincode: appData?.businessAddressPincode,
        country: appData?.businessAddressCountry
    };
    const homeAddress = appData?.homeAddress || {
        line1: appData?.homeAddressLineFirst,
        line2: appData?.homeAddressLineSecond,
        line3: appData?.homeAddressLineThird,
        city: appData?.homeAddressCity,
        state: appData?.homeAddressState,
        pincode: appData?.homeAddressPincode,
        country: appData?.homeAddressCountry
    };
    const loanInfo = appData?.loanInfo || appData || {};
    const occupation = appData?.occupation || appData || {};
    const financialInfo = appData?.financialInfo || appData || {};

    console.log("Primary Application Data >> ", primaryApp);
    console.log("App Data >> ", appData);
    console.log("Personal Info >> ", personal);
    console.log("Phone >> ", phone);
    console.log("Home Address >> ", homeAddress);

    // Base mapping that works for all schemas
    const baseMapping = {
        // Application Information
        applicationDate: new Date().toISOString().slice(0, 10),
        applicationNumber: personal.applicationCode || "",
        customerNumber: personal.applicationCode || "",

        // Personal Information - Full Name Group
        fullName: {
            firstName: personal.fname || "",
            middleName: personal.mname || "",
            lastName: personal.lname || "",
        },

        // Basic Personal Details
        gender: personal.gender || "",
        age: calculateAge(personal.dateOfBirth) || "",
        nationality: personal.nationality || "",
        motherName: personal.motherName || "",
        maritalStatus: personal.maritalStatus || "",

        // Contact Information Group
        contactInfo: {
            phoneCode: phone.code || "+91",
            phone: phone.number || "",
            email: personal.email || "",
        },

        // Alternate Contact Information
        alternateContactInfo: {
            phoneCode: alternatePhone.code || "+91",
            phone: alternatePhone.number || "",
        },

        // Identification Documents Group
        identification: {
            pan: appData?.panNumber || "",
            aadhaar: appData?.aadhaarNumber || "",
        },

        // Address Information
        address: formatAddress(homeAddress),
        businessAddress: formatAddress(businessAddress),

        // Loan Information
        loanAmount: loanInfo.loanAmount || appData?.loanAmount || "",
        loanPurpose: loanInfo.purposeOfLoan || appData?.purposeOfLoan || "",
        monthlyIncome: financialInfo.firstBorrowerFinancialIncome ||
            financialInfo.grandTotalIncome ||
            appData?.firstBorrowerIncome || "",

        // Employment/Business Information
        occupationType: occupation.type || appData?.occupationType || "",
        constitution: occupation.constitution || appData?.constitution || "",
        businessExperience: occupation.experience || appData?.applicantExperience || "",

        // Financial Information
        totalRepaymentCapacity: financialInfo.totalRepaymentCapacity || "",
        grandTotalIncome: financialInfo.grandTotalIncome || "",

        // Additional borrower information
        firstBorrowerName: financialInfo.firstBorrowerName || "",
        secondBorrowerName: financialInfo.secondBorrowerName || "",
        thirdBorrowerName: financialInfo.thirdBorrowerName || "",
    };

    // If no schema provided, return base mapping
    if (!schema) {
        return baseMapping;
    }

    // Schema-specific mapping
    const mappedData = mapDataToSchema(baseMapping, primaryApp, schema);

    // Apply schema-specific mappers if available
    const schemaCode = schema.loanCode || schema.code;
    if (schemaCode && schemaSpecificMappers[schemaCode]) {
        return schemaSpecificMappers[schemaCode](mappedData, primaryApp);
    }

    return mappedData;
};

/**
 * Maps data to specific schema structure
 * @param {Object} baseMapping - Base mapped data
 * @param {Object} primaryApp - Original primary application data
 * @param {Object} schema - Target schema
 * @returns {Object} Schema-specific mapped data
 */
const mapDataToSchema = (baseMapping, primaryApp, schema) => {
    const mappedData = { ...baseMapping };

    if (!schema.sections) return mappedData;

    // Iterate through schema sections and fields to map data appropriately
    schema.sections.forEach(section => {
        if (section.fields) {
            section.fields.forEach(field => {
                mapFieldData(field, mappedData, primaryApp);
            });
        }
    });

    return mappedData;
};

/**
 * Maps data for individual fields based on field type and name
 * @param {Object} field - Schema field definition
 * @param {Object} mappedData - Current mapped data object
 * @param {Object} primaryApp - Original primary application data
 */
const mapFieldData = (field, mappedData, primaryApp) => {
    const fieldName = field.name;

    // Handle group fields recursively
    if (field.type === 'group' && field.fields) {
        field.fields.forEach(subField => {
            mapFieldData(subField, mappedData, primaryApp);
        });
        return;
    }

    // Handle table fields
    if (field.type === 'table') {
        // For existing loans table
        if (fieldName === 'existingLoans') {
            mappedData[fieldName] = getExistingLoansData(primaryApp);
        }
        return;
    }

    // Handle specific field mappings
    switch (fieldName) {
        case 'selectedPrimaryApplicantId':
            mappedData[fieldName] = primaryApp?.primaryApplication?._id || "";
            break;

        case 'riskCategory':
            mappedData[fieldName] = determineRiskCategory(primaryApp);
            break;

        case 'ckycrNumber':
            mappedData[fieldName] = primaryApp?.primaryApplication?.ckycrNumber || "";
            break;

        case 'hasExistingLoans':
            mappedData[fieldName] = hasExistingLoans(primaryApp) ? 'yes' : 'no';
            break;

        case 'guarantorRequired':
            mappedData[fieldName] = 'no'; // Default to no, can be updated by user
            break;

        case 'loanTerm':
            mappedData[fieldName] = primaryApp?.primaryApplication?.loanTerm || 20;
            break;

        case 'moratoriumPeriod':
            mappedData[fieldName] = primaryApp?.primaryApplication?.moratoriumPush || 0;
            break;

        case 'propertyAddress':
            mappedData[fieldName] = formatAddress(primaryApp?.primaryApplication?.propertyAddress || {});
            break;

        case 'propertyType':
            mappedData[fieldName] = primaryApp?.primaryApplication?.propertyType || "";
            break;

        case 'landArea':
            mappedData[fieldName] = primaryApp?.primaryApplication?.landArea || "";
            break;

        case 'constructionArea':
            mappedData[fieldName] = primaryApp?.primaryApplication?.constructionArea || "";
            break;

        case 'declaration':
            mappedData[fieldName] = false; // User must explicitly agree
            break;

        default:
            // Try to find matching field in base mapping or primary app data
            if (mappedData[fieldName] === undefined) {
                mappedData[fieldName] = findFieldValue(fieldName, primaryApp);
            }
            break;
    }
};

/**
 * Helper function to calculate age from date of birth
 * @param {string} dateOfBirth - Date of birth string
 * @returns {number} Age in years
 */
const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
};

/**
 * Helper function to format address from address object
 * @param {Object} addressObj - Address object with line1, line2, etc.
 * @returns {string} Formatted address string
 */
const formatAddress = (addressObj) => {
    if (!addressObj) return "";

    const parts = [
        addressObj.line1,
        addressObj.line2,
        addressObj.line3,
        addressObj.city,
        addressObj.state,
        addressObj.pincode,
        addressObj.country
    ].filter(part => part && part.trim() !== "");

    return parts.join(", ");
};

/**
 * Helper function to determine risk category based on primary application data
 * @param {Object} primaryApp - Primary application data
 * @returns {string} Risk category (low, medium, high)
 */
const determineRiskCategory = (primaryApp) => {
    const financialInfo = primaryApp?.primaryApplication?.financialInfo || {};
    const income = parseFloat(financialInfo.grandTotalIncome || 0);
    const loanAmount = parseFloat(primaryApp?.primaryApplication?.loanAmount || 0);

    // Simple risk assessment logic
    if (income > 100000 && loanAmount < income * 60) {
        return 'low';
    } else if (income > 50000 && loanAmount < income * 80) {
        return 'medium';
    } else {
        return 'high';
    }
};

/**
 * Helper function to check if applicant has existing loans
 * @param {Object} primaryApp - Primary application data
 * @returns {boolean} True if has existing loans
 */
const hasExistingLoans = (primaryApp) => {
    // Check if there's any indication of existing loans in the primary application
    const existingLoans = primaryApp?.primaryApplication?.existingLoans;
    const hasLoans = primaryApp?.primaryApplication?.hasExistingLoans;

    return hasLoans === 'yes' || hasLoans === true ||
        (Array.isArray(existingLoans) && existingLoans.length > 0);
};

/**
 * Helper function to get existing loans data
 * @param {Object} primaryApp - Primary application data
 * @returns {Array} Array of existing loan objects
 */
const getExistingLoansData = (primaryApp) => {
    const existingLoans = primaryApp?.primaryApplication?.existingLoans || [];

    if (Array.isArray(existingLoans) && existingLoans.length > 0) {
        return existingLoans.map(loan => ({
            bank: loan.bank || loan.financialInstitution || "",
            loanType: loan.loanType || loan.type || "",
            limit: loan.limit || loan.sanctionedAmount || "",
            outstanding: loan.outstanding || loan.outstandingAmount || "",
            collateral: loan.collateral || loan.security || "",
            remarks: loan.remarks || loan.additionalInfo || ""
        }));
    }

    // Return empty array with one row for user to fill
    return [{
        bank: "",
        loanType: "",
        limit: "",
        outstanding: "",
        collateral: "",
        remarks: ""
    }];
};

/**
 * Helper function to find field value in primary application data
 * @param {string} fieldName - Field name to search for
 * @param {Object} primaryApp - Primary application data
 * @returns {any} Field value or empty string
 */
const findFieldValue = (fieldName, primaryApp) => {
    // Direct field mapping
    const directMappings = {
        gstNumber: primaryApp?.primaryApplication?.gstNumber,
        panNumber: primaryApp?.primaryApplication?.panNumber,
        businessRegistrationNumber: primaryApp?.primaryApplication?.businessRegistrationNumber,
        businessSector: primaryApp?.primaryApplication?.businessSector,
        typeOfLoan: primaryApp?.primaryApplication?.typeOfLoan,
        interestType: primaryApp?.primaryApplication?.interestType,
        rateOfInterest: primaryApp?.primaryApplication?.rateOfInterest,
        propertyOwnerName: primaryApp?.primaryApplication?.propertyOwnerName,
        valuation: primaryApp?.primaryApplication?.valuation,
        valuationDate: primaryApp?.primaryApplication?.valuationDate,
        surveyNumber: primaryApp?.primaryApplication?.surveyNumber,
        ownershipDetails: primaryApp?.primaryApplication?.ownershipDetails,
        titleClearance: primaryApp?.primaryApplication?.titleClearance,
        approvedPlan: primaryApp?.primaryApplication?.approvedPlan,
        commencementCertificate: primaryApp?.primaryApplication?.commencementCertificate,
        completionCertificate: primaryApp?.primaryApplication?.completionCertificate,
        constructionCost: primaryApp?.primaryApplication?.constructionCost,
        purchasePrice: primaryApp?.primaryApplication?.purchasePrice,
        renovationCost: primaryApp?.primaryApplication?.renovationCost,
    };

    // Check direct mappings first
    if (directMappings[fieldName] !== undefined) {
        return directMappings[fieldName] || "";
    }

    // Try to find in nested objects
    const searchInObject = (obj, key) => {
        if (!obj || typeof obj !== 'object') return "";

        for (const [objKey, value] of Object.entries(obj)) {
            if (objKey === key) return value || "";
            if (typeof value === 'object' && value !== null) {
                const found = searchInObject(value, key);
                if (found) return found;
            }
        }
        return "";
    };

    const found = searchInObject(primaryApp, fieldName);
    return found || "";
};

/**
 * Schema-specific data mappers for different loan types
 */
export const schemaSpecificMappers = {
    /**
     * Home Loan specific mapping
     */
    HL: (baseMapping, primaryApp) => {
        return {
            ...baseMapping,
            // Home loan specific fields
            propertyMeasurements: {
                landArea: primaryApp?.primaryApplication?.landArea || "",
                constructionArea: primaryApp?.primaryApplication?.constructionArea || "",
            },
            // Map loan purpose to home loan specific values
            loanPurpose: mapHomeLoanPurpose(primaryApp?.primaryApplication?.purposeOfLoan),
        };
    },

    /**
     * Business Loan specific mapping
     */
    BL: (baseMapping, primaryApp) => {
        return {
            ...baseMapping,
            // Business loan specific fields
            businessDetails: {
                gstNumber: primaryApp?.primaryApplication?.gstNumber || "",
                businessSector: primaryApp?.primaryApplication?.businessSector || "",
                businessRegistrationNumber: primaryApp?.primaryApplication?.businessRegistrationNumber || "",
                dateOfBusinessStart: primaryApp?.primaryApplication?.occupation?.dateOfBusinessStart || "",
            },
        };
    },

    /**
     * Education Loan specific mapping
     */
    EL: (baseMapping, primaryApp) => {
        return {
            ...baseMapping,
            // Education loan specific fields
            courseDetails: {
                courseName: primaryApp?.primaryApplication?.courseName || "",
                instituteName: primaryApp?.primaryApplication?.instituteName || "",
                courseDuration: primaryApp?.primaryApplication?.courseDuration || "",
                courseFee: primaryApp?.primaryApplication?.courseFee || "",
            },
        };
    },
};

/**
 * Helper function to map loan purpose to home loan specific values
 * @param {string} purpose - Original loan purpose
 * @returns {string} Mapped home loan purpose
 */
const mapHomeLoanPurpose = (purpose) => {
    if (!purpose) return "";

    const purposeMap = {
        'construction': 'construction',
        'purchase': 'purchase',
        'renovation': 'renovation',
        'building': 'construction',
        'buying': 'purchase',
        'repair': 'renovation',
        'extension': 'renovation',
    };

    const lowerPurpose = purpose.toLowerCase();
    for (const [key, value] of Object.entries(purposeMap)) {
        if (lowerPurpose.includes(key)) {
            return value;
        }
    }

    return purpose;
};

/**
 * Debug utility to inspect primary application data structure
 * @param {Object} primaryApp - Primary application data
 */
export const debugPrimaryApplicationData = (primaryApp) => {
    console.log("=== Primary Application Data Debug ===");
    console.log("Full data:", primaryApp);
    console.log("Keys at root level:", Object.keys(primaryApp || {}));

    if (primaryApp?.primaryApplication) {
        console.log("primaryApplication exists, keys:", Object.keys(primaryApp.primaryApplication));
        console.log("personalInfo:", primaryApp.primaryApplication.personalInfo);
        console.log("phone:", primaryApp.primaryApplication.phone);
        console.log("homeAddress:", primaryApp.primaryApplication.homeAddress);
    } else {
        console.log("No primaryApplication wrapper, checking direct fields:");
        console.log("fname:", primaryApp?.fname);
        console.log("phoneNumber:", primaryApp?.phoneNumber);
        console.log("homeAddressLineFirst:", primaryApp?.homeAddressLineFirst);
    }
    console.log("=====================================");
};

/**
 * Enhanced data mapper that automatically detects schema and applies appropriate mapping
 * @param {Object} primaryApp - Primary application data
 * @param {Object} schema - Form schema (optional, will use base mapping if not provided)
 * @param {Object} options - Additional options for mapping
 * @returns {Object} Mapped form values
 */
export const mapPrimaryApplicationToForm = (primaryApp, schema = null, options = {}) => {
    const {
        includeEmptyFields = true,
        validateData = false,
        customMappings = {}
    } = options;

    // Get base mapped data
    let mappedData = parsePrimaryDataToFormValues(primaryApp, schema);

    // Apply custom mappings if provided
    if (Object.keys(customMappings).length > 0) {
        mappedData = { ...mappedData, ...customMappings };
    }

    // Remove empty fields if requested
    if (!includeEmptyFields) {
        mappedData = removeEmptyFields(mappedData);
    }

    // Validate data if requested
    if (validateData && schema) {
        mappedData = validateMappedData(mappedData, schema);
    }

    return mappedData;
};

/**
 * Helper function to remove empty fields from mapped data
 * @param {Object} data - Mapped data object
 * @returns {Object} Data with empty fields removed
 */
const removeEmptyFields = (data) => {
    const cleaned = {};

    for (const [key, value] of Object.entries(data)) {
        if (value !== null && value !== undefined && value !== "") {
            if (typeof value === 'object' && !Array.isArray(value)) {
                const cleanedNested = removeEmptyFields(value);
                if (Object.keys(cleanedNested).length > 0) {
                    cleaned[key] = cleanedNested;
                }
            } else if (Array.isArray(value) && value.length > 0) {
                cleaned[key] = value;
            } else if (typeof value !== 'object') {
                cleaned[key] = value;
            }
        }
    }

    return cleaned;
};

/**
 * Helper function to validate mapped data against schema
 * @param {Object} data - Mapped data
 * @param {Object} schema - Form schema
 * @returns {Object} Validated data with warnings
 */
const validateMappedData = (data, schema) => {
    const warnings = [];
    const validatedData = { ...data };

    // Basic validation logic
    if (schema && schema.sections) {
        schema.sections.forEach(section => {
            if (section.fields) {
                section.fields.forEach(field => {
                    if (field.required && (!data[field.name] || data[field.name] === "")) {
                        warnings.push(`Required field '${field.label || field.name}' is missing`);
                    }
                });
            }
        });
    }

    validatedData._warnings = warnings;
    return validatedData;
};

// Export all utility functions for external use
export {
    calculateAge,
    formatAddress,
    determineRiskCategory,
    hasExistingLoans,
    getExistingLoansData,
    findFieldValue,
    mapHomeLoanPurpose
};
