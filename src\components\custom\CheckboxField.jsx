/* eslint-disable react/prop-types */
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "../ui/form";
import { Checkbox } from "../ui/checkbox";

function CheckboxField({
  control,
  name,
  label,
  isRequired = false,
  description,
  helpText,
}) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
          <FormControl>
            <Checkbox
              checked={field.value}
              onCheckedChange={field.onChange}
              disabled={field.disabled}
              className="mt-1"
            />
          </FormControl>
          <div className="space-y-2 leading-none flex-1">
            <FormLabel className="text-sm font-medium leading-relaxed">
              {label}
              {isRequired && <span className="text-red-500 ml-1">*</span>}
            </FormLabel>
            {description && (
              <FormDescription className="text-sm text-gray-600 leading-relaxed whitespace-pre-line">
                {description}
              </FormDescription>
            )}
            {helpText && !description && (
              <FormDescription className="text-xs text-gray-500">
                {helpText}
              </FormDescription>
            )}
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  );
}

export default CheckboxField;
