/* eslint-disable react/prop-types */
import { useState } from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const DateField = ({ control, name, label, isRequired = false }) => {
  const [open, setOpen] = useState(false);

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem>
          <FormLabel htmlFor={field.name}>
            {label} {isRequired ? <span className="text-red-500">*</span> : ""}
          </FormLabel>
          <FormControl>
            <div className="flex flex-col gap-1">
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger>
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setOpen(!open)} // ✅ Ensure the popover opens
                    className={cn(
                      "w-full h-[40px] pl-3 text-left font-normal rounded-md flex items-center justify-between hover:bg-white",
                      !field.value && "text-muted-foreground",
                      fieldState.error
                        ? "border-red-500 focus:ring-red-500"
                        : "border-input"
                    )}
                  >
                    {field.value ? (
                      format(new Date(field.value), "dd/MM/yyyy")
                    ) : (
                      <span>DD/MM/YYYY</span>
                    )}
                    <CalendarIcon className="h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value ? new Date(field.value) : undefined}
                    onSelect={(date) => {
                      field.onChange(date || null);
                      setOpen(false);
                    }}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                    fromYear={1900}
                    toYear={new Date().getFullYear()}
                    initialFocus
                    captionLayout="dropdown"
                  />
                </PopoverContent>
              </Popover>
              {fieldState.error && (
                <FormMessage className="text-red-500">
                  {fieldState.error.message}
                </FormMessage>
              )}
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
};

export default DateField;
