/* eslint-disable react/prop-types */
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import DateField from "@/components/custom/DateField";
import AutoScrollArea from "../../components/custom/AutoScrollArea";
import { Button } from "@/components/ui/button";
import { Pencil, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import PersonalInfoDialog from "../../Dialog/PersonalInfoDialog";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { Label } from "@/components/ui/label";
import { getLastFiveFinancialYears } from "../../utils/dateTime";
import FormSelect from "../../components/custom/SelectInput";
import RadioButton from "../../components/custom/RadioButton";
import { occupations } from "../../constants/occupationType";
import { useIncomeCalculation } from "../../hooks/useCalculationForIncome";
import { useLocationDropdowns } from "../../hooks/useLocationDropdown";
import { useGetProductMutation } from "../../store/actions";
import { useSelector } from "react-redux";
import InputField from "../../components/custom/InputField";
import { mapToLabelValueArray } from "../../lib/utils";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  address: z.string().min(1, "Address is required"),
  phone: z.string().min(10, "Phone must be at least 10 digits"),
});

const PersonalInfo = ({ control, getValues, setValue, errors }) => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: { name: "", address: "", phone: "" },
  });

  const fiveYears = getLastFiveFinancialYears();

  const { setValue: updateFormValue, reset } = form;

  const [getLoanTypes] = useGetProductMutation();

  useEffect(() => {
    getLoanTypes({ page: 1, limit: 5000000 });
  }, []);

  const { data } = useSelector((state) => state.product);

  const loanTypes = mapToLabelValueArray(data, "loanTypeId.name", "_id");

  const productSelected = useWatch({ control, name: "loanProductId" });
  const selectedProductData = data?.filter((i) => i?._id === productSelected);

  const isSameAddress = useWatch({ control, name: "isSameAddress" });

  useEffect(() => {
    const firstErrorKey = Object.keys(errors)[0];
    if (firstErrorKey) {
      const el = document.querySelector(`[name="${firstErrorKey}"]`);
      if (el && typeof el.scrollIntoView === "function") {
        el.scrollIntoView({ behavior: "smooth", block: "center" });
        el.focus(); // optional, if scroll is not enough
      }
    }
  }, [errors]);

  const personalAddressFields = [
    "homeAddressLineFirst",
    "homeAddressLineSecond",
    "homeAddressLineThird",
    "homeAddressPincode",
    "homeAddressCity",
    "homeAddressState",
    "homeAddressCountry",
  ];

  const personalAddressValues = useWatch({
    control,
    name: personalAddressFields,
  });

  const {
    countriesOptions: homeCountries,
    statesOptions: homeStates,
    citiesOptions: homeCities,
  } = useLocationDropdowns({
    country: "homeAddressCountry",
    state: "homeAddressState",
    city: "homeAddressCity",
  });

  const {
    countriesOptions: businessCountries,
    statesOptions: businessStates,
    citiesOptions: businessCities,
  } = useLocationDropdowns({
    country: "businessAddressCountry",
    state: "businessAddressState",
    city: "businessAddressCity",
  });

  const { firstName, secondName } = useIncomeCalculation(control, setValue);

  useEffect(() => {
    if (isSameAddress) {
      personalAddressFields.forEach((field, index) =>
        setValue(
          field.replace("home", "business"),
          personalAddressValues[index]
        )
      );
    }
  }, [isSameAddress, personalAddressValues, setValue]);

  const onSubmitDialog = (values) => {
    let updatedDetails = [...applicantDetails];

    if (editIndex !== null) {
      updatedDetails[editIndex] = values;
    } else {
      updatedDetails.push(values);
    }

    setValue("applicantDetails", updatedDetails);

    reset();
    setDialog(false);
    setEditIndex(null);
  };

  const [dialog, setDialog] = useState(false);
  const [editIndex, setEditIndex] = useState(null);

  const handleDelete = (index) => {
    const updatedDetails = applicantDetails.filter((_, i) => i !== index);
    setValue("applicantDetails", updatedDetails);
  };

  const handleEdit = (index) => {
    setEditIndex(index);
    setDialog(true);

    const selectedData = getValues("applicantDetails")[index];

    if (selectedData) {
      Object.keys(selectedData).forEach((key) => {
        updateFormValue(key, selectedData[key]);
      });
    }
  };

  const applicantDetails = getValues("applicantDetails");
  const occupationType = useWatch({ control, name: "occupationType" });

  return (
    <AutoScrollArea ids={["page-header", "progressBar"]}>
      <div className="flex flex-col gap-6 pb-2">
        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="fname"
              label="First Name"
              placeholder="Enter First Name"
              isRequired
            />
          </div>
          <div className="flex-1">
            <InputField
              control={control}
              name="mname"
              label="Middle Name"
              placeholder="Enter Middle Name"
              isRequired
            />
          </div>
          <div className="flex-1">
            <InputField
              control={control}
              name="lname"
              label="Last Name"
              placeholder="Enter Last Name"
              isRequired
            />
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="motherName"
              label="Mother's Name"
              placeholder="Enter Mother's Name"
              isRequired
            />
          </div>
          <div className="flex-1">
            <DateField
              control={control}
              name="dateOfBirth"
              label="Date of Birth"
              isRequired
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <RadioButton
              name="gender"
              label="Gender"
              isRequired
              formControl={control}
              options={[
                { label: "Male", value: "male" },
                { label: "Female", value: "female" },
                { label: "Trans Gender", value: "transGender" },
              ]}
            />
          </div>
          <div className="flex-1">
            <RadioButton
              name="maritalStatus"
              label="Marital status"
              formControl={control}
              isRequired
              options={[
                { label: "Married", value: "married" },
                { label: "Unmarried", value: "unmarried" },
                { label: "Others", value: "other" },
              ]}
            />
          </div>
          <div className="flex-1">
            <RadioButton
              name="nationality"
              label="Nationality"
              formControl={control}
              isRequired
              options={[
                { label: "Indian", value: "indian" },
                { label: "Others", value: "other" },
              ]}
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <FormItem className="flex flex-col flex-1">
            <FormLabel>
              Phone Number <span className="text-red-500">*</span>
            </FormLabel>
            <div className="flex flex-row flex-1 gap-2.5">
              <div className="w-16">
                <FormField
                  control={control}
                  name="phoneCode"
                  render={({ field }) => (
                    <FormControl>
                      <Input
                        disabled
                        placeholder="Phone Code"
                        className="h-[40px]"
                        {...field}
                      />
                    </FormControl>
                  )}
                />
              </div>
              <FormField
                control={control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter Phone Number"
                      className="h-[40px]"
                      {...field}
                    />
                  </FormControl>
                )}
              />
            </div>
          </FormItem>

          <FormItem className="flex flex-col flex-1">
            <FormLabel>Alternate Phone Number</FormLabel>
            <div className="flex flex-row flex-1 gap-2.5">
              <div className="w-16">
                <FormField
                  control={control}
                  name="alternatePhoneCode"
                  render={({ field }) => (
                    <FormControl>
                      <Input
                        disabled
                        placeholder="Phone Code"
                        className="h-[40px]"
                        {...field}
                      />
                    </FormControl>
                  )}
                />
              </div>
              <FormField
                control={control}
                name="alternatePhoneNumber"
                render={({ field }) => (
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter Alternate Phone Number"
                      className="h-[40px]"
                      {...field}
                    />
                  </FormControl>
                )}
              />
            </div>
          </FormItem>

          <div className="flex-1">
            <InputField
              control={control}
              name="email"
              label="Email"
              placeholder="Enter Email"
              type="email"
              isRequired
            />
          </div>
        </div>

        <div className="flex-1">
          <FormSelect
            label="Loan Type"
            name="loanProductId"
            isRequired
            control={control}
            options={loanTypes}
          />
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <FormSelect
              label="Applicant's Occupation Type"
              name="occupationType"
              control={control}
              isRequired
              options={occupations}
            />
          </div>

          {occupationType === "other" && (
            <div className="flex-1">
              <InputField
                control={control}
                name="occupationTypeName"
                label="Other Occupation"
                placeholder="Enter Other Occupation"
                isRequired
              />
            </div>
          )}

          <div className="flex-1">
            <InputField
              control={control}
              name="constitution"
              label="Constitution"
              placeholder="Enter Constitution"
              isRequired
            />
          </div>

          <div className="flex-1">
            <DateField
              control={control}
              name="businessStartDate"
              label="Business Start Date"
              isRequired
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="purposeOfLoan"
              label="Purpose of Loan"
              placeholder="Enter Purpose of Loan"
              isRequired
            />
          </div>
          <div className="flex-1">
            <InputField
              control={control}
              name="applicantExperience"
              label="Applicant's experience with the product/trade item."
              placeholder="Enter Applicant's experience with the product/trade item."
              isRequired
            />
          </div>
        </div>

        <div className="flex flex-col gap-1.5">
          <div className="flex flex-1 justify-end">
            <Button size="icon" type="button" onClick={() => setDialog(true)}>
              <Plus />
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow className="border-0 bg-gradient-to-r from-gray-50 to-gray-100/80 hover:from-gray-100 hover:to-gray-200/80 transition-all duration-200">
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Name
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Address
                </TableHead>
                <TableHead className="font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Phone
                </TableHead>
                <TableHead className="border-0 font-bold text-gray-800 py-4 px-6 text-sm uppercase tracking-wider">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {applicantDetails?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item?.name}</TableCell>
                  <TableCell>{item?.address}</TableCell>
                  <TableCell>{item?.phone}</TableCell>
                  <TableCell>
                    <div className="flex flex-row gap-1.5 items-center">
                      <Button
                        size="icon"
                        className="text-amber-400 border-amber-400 hover:border-amber-400/70 hover:text-amber-400/70"
                        variant="outline"
                        onClick={() => {
                          handleEdit(index);
                        }}
                      >
                        <Pencil />
                      </Button>
                      <Button
                        size="icon"
                        className="text-red-500 border-red-500 hover:border-red-500/70 hover:text-red-500/70"
                        variant="outline"
                        onClick={() => handleDelete(index)}
                      >
                        <Trash2 />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="flex flex-col gap-2.5">
          <div className="flex-1">
            <Label>
              Personal Address <span className="text-red-500">*</span>
            </Label>
          </div>
          <div className="flex flex-col md:flex-row gap-2.5">
            <div className="flex-1">
              <InputField
                control={control}
                name="homeAddressLineFirst"
                placeholder="Home Address Line 1"
              />
            </div>

            <div className="flex-1">
              <InputField
                control={control}
                name="homeAddressLineSecond"
                placeholder="Home Address Line 2"
              />
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-2.5">
            <div className="flex-1">
              <InputField
                control={control}
                name="homeAddressLineThird"
                placeholder="Home Address Line 3"
              />
            </div>

            <div className="flex-1">
              <InputField
                control={control}
                name="homeAddressPincode"
                placeholder="Pincode"
              />
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-2.5">
            <div className="flex-1">
              <FormSelect
                label="Country"
                hideLabel={true}
                name="homeAddressCountry"
                control={control}
                options={homeCountries}
              />
            </div>

            <div className="flex-1">
              <FormSelect
                label="State"
                hideLabel={true}
                name="homeAddressState"
                control={control}
                options={homeStates}
              />
            </div>

            <div className="flex-1">
              <FormSelect
                label="City"
                hideLabel={true}
                name="homeAddressCity"
                control={control}
                options={homeCities}
              />
            </div>
          </div>
        </div>

        <div>
          <FormField
            control={control}
            name="isSameAddress"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Same as Personal Address</FormLabel>
                </div>
              </FormItem>
            )}
          />
        </div>

        <div className="flex flex-col gap-2.5">
          <div className="flex-1">
            <Label>
              Applicant / Business / Office / Address{" "}
              <span className="text-red-500">*</span>
            </Label>
          </div>
          <div className="flex flex-col md:flex-row gap-2.5">
            <div className="flex-1">
              <InputField
                control={control}
                name="businessAddressLineFirst"
                placeholder="Business Address Line 1"
              />
            </div>

            <div className="flex-1">
              <InputField
                control={control}
                name="businessAddressLineSecond"
                placeholder="Business Address Line 2"
              />
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-2.5">
            <div className="flex-1">
              <InputField
                control={control}
                name="businessAddressLineThird"
                placeholder="Business Address Line 3"
              />
            </div>

            <div className="flex-1">
              <InputField
                control={control}
                name="businessAddressPincode"
                placeholder="Pincode"
              />
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-2.5">
            <div className="flex-1">
              <FormSelect
                label="Country"
                hideLabel={true}
                name="businessAddressCountry"
                control={control}
                options={businessCountries}
              />
            </div>

            <div className="flex-1">
              <FormSelect
                label="State"
                hideLabel={true}
                name="businessAddressState"
                control={control}
                options={businessStates}
              />
            </div>

            <div className="flex-1">
              <FormSelect
                label="City"
                hideLabel={true}
                name="businessAddressCity"
                control={control}
                options={businessCities}
              />
            </div>
          </div>
        </div>

        <h2 className="text-primary-color font-extrabold text-2xl">
          Personal Finance
        </h2>

        <div className="flex flex-col xl:flex-row gap-2.5">
          <div className="flex-1">
            <FormSelect
              label="Last Financial Year"
              name="financialYear"
              isRequired
              control={control}
              options={fiveYears.map((item) => {
                return { value: item, name: item };
              })}
            />
          </div>

          <div className="flex-1">
            <InputField
              control={control}
              name="firstBorrowerName"
              placeholder="Enter Name of First Borrower"
              label="First Borrower Name"
              isRequired
            />
          </div>

          <div className="flex-1">
            <InputField
              control={control}
              name="firstBorrowerFinancialIncome"
              placeholder="Enter First Borrower Income"
              label="First Borrower Income"
              type="number"
              isRequired
            />
          </div>
        </div>

        {selectedProductData[0]?.isJoint && firstName && (
          <div className="flex flex-col xl:flex-row gap-2.5">
            <div className="flex-1">
              <InputField
                control={control}
                name="secondBorrowerName"
                placeholder="Enter Name of Second Borrower"
                label="Second Borrower Name"
              />
            </div>

            <div className="flex-1">
              <InputField
                control={control}
                name="secondBorrowerFinancialIncome"
                placeholder="Enter Second Borrower Income"
                type="number"
                label="Second Borrower Income"
              />
            </div>
          </div>
        )}

        {selectedProductData[0]?.isJoint && secondName && (
          <div className="flex flex-col xl:flex-row gap-2.5">
            <div className="flex-1">
              <InputField
                control={control}
                name="thirdBorrowerName"
                placeholder="Enter Third Borrower Name"
                label="Third Borrower Name"
              />
            </div>

            <div className="flex-1">
              <InputField
                control={control}
                name="thirdBorrowerFinancialIncome"
                placeholder="Enter Third Borrower Income"
                type="number"
                label="Third Borrower Income"
              />
            </div>
          </div>
        )}

        <div className="flex flex-col xl:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="grandTotalIncome"
              placeholder="Total Income"
              type="number"
              label="Grand Total Income"
              isRequired
              disabled
            />
          </div>

          <div className="flex-1">
            <InputField
              control={control}
              name="roiRate"
              placeholder="Enter ROI Rate"
              type="number"
              label="ROI Rate"
              isRequired
            />
          </div>

          <div className="flex-1">
            <InputField
              control={control}
              name="totalRepaymentCapacity"
              placeholder="Total Repayment Capacity"
              type="number"
              label="Total Repayment Capacity"
              isRequired
              disabled
            />
          </div>
        </div>
      </div>
      {dialog && (
        <PersonalInfoDialog
          dialog={dialog}
          setDialog={setDialog}
          form={form}
          handleSubmit={onSubmitDialog}
        />
      )}
    </AutoScrollArea>
  );
};

export default PersonalInfo;
