/* eslint-disable react/prop-types */
import { cn } from "@/lib/utils";

const ProgressBar = ({ steps, currentStep }) => {
  return (
    <div
      className="flex items-center justify-between my-3 w-full"
      id="progressBar"
    >
      {steps.map((step, index) => (
        <div
          key={index}
          className={`flex items-center  ${
            index < steps.length - 1 ? "w-full" : null
          }`}
        >
          <div className="flex flex-col items-center">
            <div
              className={cn(
                "w-10 h-10 flex items-center justify-center rounded-full transition-colors duration-300",
                index === currentStep || step.completed === true
                  ? "bg-main-color text-white"
                  : "bg-gray-100 text-gray-500"
              )}
            >
              {step.icon}
            </div>
            <span
              className={cn(
                "mt-2 text-xs transition-colors duration-300",
                index === currentStep || step.completed === true
                  ? "text-main-text font-semibold"
                  : "text-gray-600"
              )}
            >
              {step.label}
            </span>
          </div>

          {index < steps.length - 1 && (
            <div
              className={cn(
                "flex-1 h-1 mx-4 transition-colors duration-300",
                steps[index].completed ? "bg-blue-500" : "bg-gray-300"
              )}
            ></div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ProgressBar;
