<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_35_2142)">
<rect y="0.0131836" width="20" height="20" fill="black" fill-opacity="0.01"/>
<mask id="mask0_35_2142" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="21">
<rect y="0.0131836" width="20" height="20" fill="white"/>
</mask>
<g mask="url(#mask0_35_2142)">
</g>
<g clip-path="url(#clip1_35_2142)">
<mask id="mask1_35_2142" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="21">
<rect y="0.0131836" width="20" height="20" fill="white"/>
</mask>
<g mask="url(#mask1_35_2142)">
<path d="M15 16.6799V8.34656" stroke="url(#paint0_radial_35_2142)" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10 16.6799V3.34656" stroke="url(#paint1_radial_35_2142)" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5 16.6798V11.6798" stroke="url(#paint2_radial_35_2142)" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
</g>
<defs>
<radialGradient id="paint0_radial_35_2142" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(15.5 12.5132) rotate(90) scale(4.16667 0.5)">
<stop stop-color="#3B8AFF"/>
<stop offset="1" stop-color="#0478C2"/>
</radialGradient>
<radialGradient id="paint1_radial_35_2142" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.5 10.0132) rotate(90) scale(6.66667 0.5)">
<stop stop-color="#3B8AFF"/>
<stop offset="1" stop-color="#0478C2"/>
</radialGradient>
<radialGradient id="paint2_radial_35_2142" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(5.5 14.1798) rotate(90) scale(2.5 0.5)">
<stop stop-color="#3B8AFF"/>
<stop offset="1" stop-color="#0478C2"/>
</radialGradient>
<clipPath id="clip0_35_2142">
<rect width="20" height="20" fill="white" transform="translate(0 0.0131836)"/>
</clipPath>
<clipPath id="clip1_35_2142">
<rect width="20" height="20" fill="white" transform="translate(0 0.0131836)"/>
</clipPath>
</defs>
</svg>
