/* eslint-disable react/prop-types */
import AutoScrollArea from "@/components/custom/AutoScrollArea";

import InputField from "../../components/custom/InputField";
import FormSelect from "../../components/custom/SelectInput";

const LoanDetails = ({ control }) => {
  return (
    <AutoScrollArea ids={["page-header", "progressBar"]} subtractHeight={233}>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="gstNumber"
              label="GST Number"
              placeholder="Enter GST Number"
              type="number"
              isRequired
            />
          </div>
          <div className="flex-1">
            <InputField
              control={control}
              name="panNumber"
              label="PAN Number"
              placeholder="Enter PAN Number"
              type="number"
              isRequired
            />
          </div>
          <div className="flex-1">
            <InputField
              control={control}
              name="businessRegistrationNumber"
              label="Business Registration Number"
              placeholder="Enter Business Registration Number"
              type="number"
              isRequired
            />
          </div>
        </div>

        <div className="flex-1">
          <InputField
            control={control}
            name="moratoriumPush"
            label="Moratorium push"
            placeholder="Enter Moratorium push"
            type="number"
            isRequired
          />
        </div>

        <div className="flex flex-col xl:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="businessSector"
              label="Business Sector"
              placeholder="Enter Business Sector"
              isRequired
            />
          </div>

          <div className="flex-1">
            <InputField
              control={control}
              name="typeOfLoan"
              label="Type of loan"
              placeholder="Enter Type of loan"
              isRequired
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-2.5">
          <div className="flex-1">
            <InputField
              control={control}
              name="loanAmount"
              label="Loan Amount"
              placeholder="Enter Loan Amount"
              type="number"
              isRequired
            />
          </div>
          <div className="flex-1">
            <InputField
              control={control}
              name="interestType"
              label="Interest Type"
              placeholder="Enter Interest Type"
              isRequired
            />
          </div>
          <div className="flex-1">
            <InputField
              control={control}
              name="rateOfInterest"
              label="Rate Of Interest"
              placeholder="Enter Rate Of Interest"
              type="number"
              isRequired
            />
          </div>
        </div>
      </div>
    </AutoScrollArea>
  );
};

export default LoanDetails;
