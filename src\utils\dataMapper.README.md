# Enhanced Data Mapper Documentation

## Overview

The enhanced data mapper is a comprehensive utility that automatically maps primary application data to any form schema structure. It supports multiple loan types and provides intelligent field mapping with schema-specific customizations.

## Key Features

- **Universal Mapping**: Works with any form schema structure
- **Schema-Specific Logic**: Applies custom mapping rules based on loan type
- **Intelligent Field Detection**: Automatically finds and maps fields from nested data structures
- **Flexible Configuration**: Supports custom mappings and validation options
- **Type Safety**: Handles different data types and nested objects gracefully

## Main Functions

### `parsePrimaryDataToFormValues(primaryApp, schema)`

The core mapping function that transforms primary application data into form-ready values.

**Parameters:**
- `primaryApp` (Object): Primary application data from API
- `schema` (Object, optional): Form schema for enhanced mapping

**Returns:** Object with mapped form values

### `mapPrimaryApplicationToForm(primaryApp, schema, options)`

Enhanced wrapper function with additional configuration options.

**Parameters:**
- `primaryApp` (Object): Primary application data
- `schema` (Object, optional): Form schema
- `options` (Object): Configuration options
  - `includeEmptyFields` (boolean): Include fields with empty values
  - `validateData` (boolean): Validate mapped data against schema
  - `customMappings` (Object): Additional custom field mappings

## Usage Examples

### Basic Usage

```javascript
import { parsePrimaryDataToFormValues } from '@/utils/dataMapper';

// Basic mapping without schema
const mappedData = parsePrimaryDataToFormValues(primaryApplicationData);

// With schema for enhanced mapping
const mappedData = parsePrimaryDataToFormValues(primaryApplicationData, homeLoanSchema);
```

### Advanced Usage

```javascript
import { mapPrimaryApplicationToForm } from '@/utils/dataMapper';

const mappedData = mapPrimaryApplicationToForm(
  primaryApplicationData,
  homeLoanSchema,
  {
    includeEmptyFields: true,
    validateData: false,
    customMappings: {
      applicationDate: new Date().toISOString().slice(0, 10),
      customField: "Custom Value"
    }
  }
);
```

### In React Components

```javascript
// In LoanApplicationPage.jsx
useEffect(() => {
  getPrimaryApplicationData(primaryAppId).then((res) => {
    const data = res?.data?.data;
    if (data) {
      const values = mapPrimaryApplicationToForm(data, selectedSchema, {
        includeEmptyFields: true,
        customMappings: {
          applicationDate: new Date().toISOString().slice(0, 10),
        }
      });
      setFormDefaultValues(values);
    }
  });
}, [selectedSchema]);
```

### In Custom Hooks

```javascript
// In usePrimaryApplicationData.js
const transformedData = schema 
  ? mapPrimaryApplicationToForm(response.data, schema, {
      includeEmptyFields: true,
      validateData: false,
      customMappings: {
        applicationDate: new Date().toISOString().slice(0, 10),
      }
    })
  : transformPrimaryDataToFormData(response.data);
```

## Supported Field Types

### Personal Information
- Full name (firstName, middleName, lastName)
- Contact information (phone, email)
- Identification documents (PAN, Aadhaar)
- Address information (residential, business)
- Demographics (age, gender, nationality)

### Loan Information
- Loan amount and purpose
- Loan term and moratorium period
- Risk assessment category
- Monthly income and repayment capacity

### Property Details (Home Loans)
- Property address and type
- Land area and construction area
- Property measurements
- Ownership details

### Business Information (Business Loans)
- GST number and business registration
- Business sector and constitution
- Business address and experience

### Financial Information
- Existing loans and liabilities
- Income details and sources
- Guarantor information

## Schema-Specific Mappers

The system includes specialized mappers for different loan types:

### Home Loan (HL)
- Maps property measurements
- Handles construction/purchase/renovation purposes
- Processes property-specific fields

### Business Loan (BL)
- Maps business registration details
- Handles GST and business sector information
- Processes business address and experience

### Education Loan (EL)
- Maps course and institution details
- Handles education-specific requirements

## Field Mapping Logic

1. **Base Mapping**: Creates a universal mapping that works for all schemas
2. **Schema Analysis**: Iterates through schema sections and fields
3. **Field-Specific Logic**: Applies custom logic based on field names and types
4. **Schema-Specific Enhancement**: Applies loan-type-specific mappings
5. **Custom Mappings**: Overlays any custom field mappings provided

## Helper Functions

### `calculateAge(dateOfBirth)`
Calculates age from date of birth string.

### `formatAddress(addressObj)`
Formats address object into a single string.

### `determineRiskCategory(primaryApp)`
Determines risk category based on income and loan amount.

### `hasExistingLoans(primaryApp)`
Checks if applicant has existing loans.

### `getExistingLoansData(primaryApp)`
Extracts and formats existing loan information.

## Testing

Use the test file to verify functionality:

```javascript
import { runAllTests } from '@/utils/dataMapper.test';

// Run all tests
const results = runAllTests();
console.log(results);
```

## Best Practices

1. **Always pass schema**: For best results, always provide the form schema
2. **Use custom mappings**: Add custom mappings for application-specific fields
3. **Handle empty fields**: Consider whether to include empty fields based on use case
4. **Validate data**: Enable validation when data integrity is critical
5. **Test thoroughly**: Use the test utilities to verify mapping behavior

## Future Enhancements

- Add support for more loan types
- Implement advanced validation rules
- Add field transformation utilities
- Support for conditional field mapping
- Enhanced error handling and logging
