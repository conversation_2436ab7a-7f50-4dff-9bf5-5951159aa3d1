import AutoScrollArea from "@/components/custom/AutoScrollArea";
import LabelValueRow from "../../components/custom/LabelValueRow";
import { useGetPrimaryApplicationMutation } from "../../store/actions";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const ReviewPage = () => {
  const primaryApplicationId = localStorage.getItem("personalInfoId");

  console.log("primaryApplicationId >> ", primaryApplicationId);

  const [getPrimaryApplication] = useGetPrimaryApplicationMutation();

  useEffect(() => {
    getPrimaryApplication(primaryApplicationId);
  }, []);

  const { data } = useSelector((state) => state.primaryApplication);

  console.log("primaryApplicationData >> ", data);

  return (
    <AutoScrollArea ids={["page-header", "progressBar"]} subtractHeight={233}>
      <div className="flex flex-col gap-2">
        <h2 className="text-primary-color text-lg font-bold">Personal Info</h2>
        <LabelValueRow
          label={"First Name"}
          value={data?.primaryApplication?.personalInfo?.fname}
        />

        <LabelValueRow
          label={"Middle Name"}
          value={data?.primaryApplication?.personalInfo?.mname}
        />

        <LabelValueRow
          label={"Last Name"}
          value={data?.primaryApplication?.personalInfo?.lname}
        />

        <LabelValueRow
          label={"Gender"}
          value={data?.primaryApplication?.personalInfo?.gender}
        />
      </div>
    </AutoScrollArea>
  );
};

export default ReviewPage;
