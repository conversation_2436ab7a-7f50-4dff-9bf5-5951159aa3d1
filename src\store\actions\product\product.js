import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_HOST } from "../../../constants";
import { getHeaders } from "../../header/requestHeader";
import {
  setProducts,
  setProductsError,
  setProductsLoading,
} from "../../reducer/product/product";

// import { getHeaders } from "../../utils/requestHeaders";

export const productApi = createApi({
  reducerPath: "productApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_HOST}`,
  }),

  endpoints: (builder) => ({
    getProduct: builder.mutation({
      query: ({ page = 1, limit = 10 }) => ({
        url: `/product/get?page=${page}&limit=${limit}`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setProductsLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          dispatch(
            setProducts({
              data: response.data.data.data,
              currentPage: Number(response.data.data.currentPage),
              totalPages: Number(response.data.data.totalPages),
              totalProducts: Number(response.data.data.totalItems),
            })
          );
        } catch (e) {
          console.log(">>>>>setProduct error", e);
          dispatch(setProductsError(e.message || "Failed to load products"));
        }
      },
    }),

    getSingleProduct: builder.mutation({
      query: (id) => ({
        url: `/product/get/${id}`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setProductsLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          console.log(response.data.data);
          dispatch(
            setProducts({
              product: response.data.data,
            })
          );
        } catch (e) {
          console.log(">>>>>setProducts error", e);
          dispatch(setProductsError(e.message || "Failed to load product"));
        }
      },
    }),

    addProduct: builder.mutation({
      query: (userData) => ({
        url: `/product/create`,
        method: "POST",
        body: userData,
        headers: getHeaders(),
      }),
    }),

    deleteProduct: builder.mutation({
      query: (id) => ({
        url: `/product/delete/${id}`,
        method: "DELETE",
        headers: getHeaders(),
      }),
    }),
  }),
});
// Export hooks for usage in functional components
export const {
  useGetProductMutation,
  useGetSingleProductMutation,
  useAddProductMutation,
  useDeleteProductMutation,
} = productApi;
