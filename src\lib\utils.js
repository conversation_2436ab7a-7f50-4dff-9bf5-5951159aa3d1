import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export const replacePathParams = (path, pathParams) => {
  let pathToGo = path;

  if (pathParams) {
    Object.keys(pathParams).forEach((param) => {
      pathToGo = pathToGo.replace(`:${param}`, pathParams[param]);
    });
  }

  return pathToGo;
};

const getNestedValue = (obj, path) => {
  return path
    .split(".")
    .reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : ""), obj);
};

export const mapToLabelValueArray = (
  array,
  labelKey, // can be string or array of strings
  valueKey,
  previousValue,
  isRemove = false
) => {
  if (!Array.isArray(array) || array.length === 0) {
    return [];
  }

  const seen = new Set();

  return array.reduce((acc, item) => {
    const value = getNestedValue(item, valueKey);

    if (value === previousValue) return acc;
    if (isRemove && seen.has(value)) return acc;

    seen.add(value);

    let name;

    if (Array.isArray(labelKey)) {
      const [main, ...rest] = labelKey.map((key) =>
        String(getNestedValue(item, key) ?? "")
      );
      name = `${main} (${rest.join(" - ")})`;
    } else {
      name = String(getNestedValue(item, labelKey));
    }

    acc.push({ name, value });
    return acc;
  }, []);
};

export const transformLoanData = (loans) => {
  const grouped = {};

  loans.forEach((loan) => {
    const { groupName, name, _id } = loan;
    if (!grouped[groupName]) {
      grouped[groupName] = [];
    }
    grouped[groupName].push({
      name,
      value: _id,
    });
  });

  const result = Object.keys(grouped).map((group) => ({
    name: group,
    data: grouped[group],
  }));

  return result;
};
