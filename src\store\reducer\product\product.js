import { createSlice } from "@reduxjs/toolkit";

const product = createSlice({
  name: "product",
  initialState: {
    data: [],
    product: {},
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalProducts: 0,
  },
  reducers: {
    setProducts(state, action) {
      Object.entries(action.payload).forEach(([key, value]) => {
        if (key in state) {
          // Type assertion to satisfy TypeScript
          state[key] = value;
        }
      });
      state.isLoading = false;
      state.error = null;
    },
    setProductsLoading(state) {
      state.isLoading = true;
      state.error = null;
    },
    setProductsError(state, action) {
      state.isLoading = false;
      state.error = action.payload;
      state.data = [];
    },
  },
});

export const { setProducts, setProductsError, setProductsLoading } =
  product.actions;
export default product.reducer;
