/**
 * Test file for the enhanced data mapper
 * This demonstrates how the data mapper works with different schemas
 */

import { mapPrimaryApplicationToForm, parsePrimaryDataToFormValues } from './dataMapper.js';
import housingLoanSchema from '../schemas/homeLoan.js';

// Sample primary application data for testing
const samplePrimaryApplicationData = {
  primaryApplication: {
    _id: "64f1234567890abcdef12345",
    personalInfo: {
      fname: "<PERSON>",
      mname: "<PERSON>",
      lname: "<PERSON><PERSON>",
      email: "<EMAIL>",
      gender: "male",
      dateOfBirth: "1990-05-15",
      motherName: "<PERSON>",
      maritalStatus: "married",
      nationality: "indian",
      applicationCode: "APP001234"
    },
    phone: {
      code: "+91",
      number: "9876543210"
    },
    alternatePhone: {
      code: "+91",
      number: "9876543211"
    },
    homeAddress: {
      line1: "123 Main Street",
      line2: "Apartment 4B",
      line3: "Near City Mall",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400001",
      country: "India"
    },
    businessAddress: {
      line1: "456 Business Park",
      line2: "Office 201",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400002",
      country: "India"
    },
    loanInfo: {
      loanAmount: "5000000",
      purposeOfLoan: "construction",
      loanProductId: "HL001"
    },
    occupation: {
      type: "salaried",
      constitution: "individual",
      experience: "5",
      dateOfBusinessStart: "2018-01-01"
    },
    financialInfo: {
      grandTotalIncome: "1200000",
      totalRepaymentCapacity: "600000",
      firstBorrowerFinancialIncome: "800000",
      firstBorrowerName: "John Doe"
    },
    panNumber: "**********",
    aadhaarNumber: "1234 5678 9012",
    gstNumber: "27**********1Z5",
    propertyType: "apartment",
    landArea: "1200",
    constructionArea: "1000",
    loanTerm: "20",
    moratoriumPush: "6"
  }
};

/**
 * Test function to demonstrate basic data mapping
 */
export const testBasicMapping = () => {
  console.log("=== Testing Basic Data Mapping ===");

  const mappedData = parsePrimaryDataToFormValues(samplePrimaryApplicationData);

  console.log("Mapped Data:", mappedData);
  console.log("Full Name:", mappedData.fullName);
  console.log("Contact Info:", mappedData.contactInfo);
  console.log("Address:", mappedData.address);

  return mappedData;
};

/**
 * Test function to demonstrate schema-specific mapping
 */
export const testSchemaMapping = () => {
  console.log("=== Testing Schema-Specific Mapping ===");

  const mappedData = mapPrimaryApplicationToForm(
    samplePrimaryApplicationData,
    housingLoanSchema,
    {
      includeEmptyFields: true,
      validateData: false,
      customMappings: {
        applicationDate: new Date().toISOString().slice(0, 10),
        customField: "Custom Value"
      }
    }
  );

  console.log("Schema-Mapped Data:", mappedData);
  console.log("Property Measurements:", mappedData.propertyMeasurements);
  console.log("Loan Purpose:", mappedData.loanPurpose);

  return mappedData;
};

/**
 * Test function to demonstrate field counting
 */
export const testFieldCounting = () => {
  console.log("=== Testing Field Counting ===");

  const mappedData = mapPrimaryApplicationToForm(samplePrimaryApplicationData, housingLoanSchema);

  const filledFields = Object.keys(mappedData).filter(key => {
    const value = mappedData[key];
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return Object.values(value).some(v => v !== "" && v !== null && v !== undefined);
    }
    return value !== "" && value !== null && value !== undefined;
  });

  console.log(`Total fields mapped: ${Object.keys(mappedData).length}`);
  console.log(`Fields with data: ${filledFields.length}`);
  console.log("Filled fields:", filledFields);

  return {
    totalFields: Object.keys(mappedData).length,
    filledFields: filledFields.length,
    filledFieldNames: filledFields
  };
};

/**
 * Test function to demonstrate different loan types
 */
export const testDifferentLoanTypes = () => {
  console.log("=== Testing Different Loan Types ===");

  // Test with different loan codes
  const hlSchema = { ...housingLoanSchema, loanCode: "HL" };
  const blSchema = { ...housingLoanSchema, loanCode: "BL" };
  const elSchema = { ...housingLoanSchema, loanCode: "EL" };

  const hlMapped = mapPrimaryApplicationToForm(samplePrimaryApplicationData, hlSchema);
  const blMapped = mapPrimaryApplicationToForm(samplePrimaryApplicationData, blSchema);
  const elMapped = mapPrimaryApplicationToForm(samplePrimaryApplicationData, elSchema);

  console.log("Home Loan Mapping:", hlMapped.propertyMeasurements);
  console.log("Business Loan Mapping:", blMapped.businessDetails);
  console.log("Education Loan Mapping:", elMapped.courseDetails);

  return { hlMapped, blMapped, elMapped };
};

/**
 * Test with flat data structure (like from actual API)
 */
export const testFlatDataStructure = () => {
  console.log("=== Testing Flat Data Structure ===");

  const flatData = {
    fname: "John",
    mname: "Michael",
    lname: "Doe",
    email: "<EMAIL>",
    gender: "male",
    phoneCode: "+91",
    phoneNumber: "9876543210",
    homeAddressLineFirst: "123 Main Street",
    homeAddressLineSecond: "Apartment 4B",
    homeAddressCity: "Mumbai",
    homeAddressState: "Maharashtra",
    homeAddressPincode: "400001",
    panNumber: "**********",
    loanAmount: "5000000",
    purposeOfLoan: "construction"
  };

  const mappedData = parsePrimaryDataToFormValues(flatData, housingLoanSchema);

  console.log("Flat Data Mapped:", mappedData);
  console.log("Full Name:", mappedData.fullName);
  console.log("Contact Info:", mappedData.contactInfo);

  return mappedData;
};

/**
 * Run all tests
 */
export const runAllTests = () => {
  console.log("🚀 Running Enhanced Data Mapper Tests");
  console.log("=====================================");

  try {
    const basicResult = testBasicMapping();
    const schemaResult = testSchemaMapping();
    const countResult = testFieldCounting();
    const loanTypeResult = testDifferentLoanTypes();
    const flatResult = testFlatDataStructure();

    console.log("✅ All tests completed successfully!");

    return {
      basic: basicResult,
      schema: schemaResult,
      count: countResult,
      loanTypes: loanTypeResult,
      flat: flatResult
    };
  } catch (error) {
    console.error("❌ Test failed:", error);
    return { error: error.message };
  }
};

// Export sample data for external use
export { samplePrimaryApplicationData };

// Make test functions available globally for browser console testing
if (typeof window !== 'undefined') {
  window.testDataMapper = {
    runAllTests,
    testBasicMapping,
    testSchemaMapping,
    testFieldCounting,
    testDifferentLoanTypes,
    testFlatDataStructure,
    samplePrimaryApplicationData
  };
}
