import {
  useLoginUserMutation,
  useLogoutUserMutation,
} from "./authentication/authentication";

import {
  useAddBranchMutation,
  useDeleteBranchMutation,
  useGetBranchMutation,
  useGetSingleBranchMutation,
} from "./branch/branch.js";

import {
  useAddPersonalInfoMutation,
  useAddLoanDetailsMutation,
  useGetLoanTypeMutation,
  useAddCollateralDetailsMutation,
  useAddDocumentDetailsMutation,
  useGetPrimaryApplicationMutation,
  useUpdateApplicationStatusMutation,
  useGetListPrimaryApplicationMutation,
} from "./primaryApplication/primaryApplication";

import {
  useGetCountriesMutation,
  useGetCityMutation,
  useGetStateMutation,
} from "./location/location.js";

import {
  useGetProductMutation,
  useGetSingleProductMutation,
  useAddProductMutation,
  useDeleteProductMutation,
} from "./product/product.js";

import {
  useGetUsersMutation,
  useGetSingleUserMutation,
  useAddUserMutation,

} from "./authentication/user";

export {
  useLoginUserMutation,
  useLogoutUserMutation,
  useAddBranchMutation,
  useGetBranchMutation,
  useDeleteBranchMutation,
  useAddPersonalInfoMutation,
  useAddLoanDetailsMutation,
  useGetCityMutation,
  useGetCountriesMutation,
  useGetStateMutation,
  useGetLoanTypeMutation,
  useGetSingleBranchMutation,
  useGetProductMutation,
  useGetSingleProductMutation,
  useAddProductMutation,
  useDeleteProductMutation,
  useAddCollateralDetailsMutation,
  useAddDocumentDetailsMutation,
  useGetPrimaryApplicationMutation,
  useUpdateApplicationStatusMutation,
  useGetListPrimaryApplicationMutation,
  useGetUsersMutation,
  useGetSingleUserMutation,
  useAddUserMutation,
};
