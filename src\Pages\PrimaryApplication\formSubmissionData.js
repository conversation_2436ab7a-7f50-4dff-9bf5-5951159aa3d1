export const getPersonalInfoData = (data) => ({
  personalInfo: {
    fname: data.fname,
    mname: data.mname,
    lname: data.lname,
    email: data.email,
    gender: data.gender,
    motherName: data.motherName,
    dateOfBirth: data.dateOfBirth,
    maritalStatus: data.maritalStatus,
  },
  phone: {
    code: data.phoneCode,
    number: data.phoneNumber,
  },
  alternatePhone: {
    code: data.alternatePhoneCode,
    number: data.alternatePhoneNumber,
  },
  loanInfo: {
    loanProductId: data.loanProductId,
    applicationType: data.applicationType,
    purposeOfLoan: data.purposeOfLoan,
    yearlyRepayment: data.yearlyRepayment,
    totalYearlyRepayment: data.totalYearlyRepayment,
  },
  occupation: {
    type: data.occupationType,
    typeName: data.occupationTypeName,
    constitution: data.constitution,
    dateOfBusinessStart: data.dateOfBusinessStart,
    experience: data.applicantExperience,
  },
  financialInfo: {
    financialYear: data.financialYear,
    totalRepaymentCapacity: data.totalRepaymentCapacity,
    grandTotalIncome: data.grandTotalIncome,
    roiRate: data.roiRate,
    firstBorrowerFinancialIncome: data.firstBorrowerFinancialIncome,
    secondBorrowerFinancialIncome: data.secondBorrowerFinancialIncome,
    thirdBorrowerFinancialIncome: data.thirdBorrowerFinancialIncome,
    firstBorrowerName: data.firstBorrowerName,
    secondBorrowerName: data.secondBorrowerName,
    thirdBorrowerName: data.thirdBorrowerName,
  },
  applicantDetails: data.applicantDetails,
  businessAddress: {
    line1: data.businessAddressLineFirst,
    line2: data.businessAddressLineSecond,
    line3: data.businessAddressLineThird,
    city: data.businessAddressCity,
    state: data.businessAddressState,
    pincode: data.businessAddressPincode,
    country: data.businessAddressCountry,
  },
  homeAddress: {
    line1: data.homeAddressLineFirst,
    line2: data.homeAddressLineSecond,
    line3: data.homeAddressLineThird,
    city: data.homeAddressCity,
    state: data.homeAddressState,
    pincode: data.homeAddressPincode,
    country: data.homeAddressCountry,
  },
});

export const getLoanDetailsData = (data) => ({
  gstNumber: data.gstNumber,
  panNumber: data.panNumber,
  businessRegistrationNumber: data.businessRegistrationNumber,
  moratoriumPush: data.moratoriumPush,
  businessSector: data.businessSector,
  typeOfLoan: data.typeOfLoan,
  loanAmount: data.loanAmount,
  interestType: data.interestType,
  rateOfInterest: data.rateOfInterest,
});

export const getCollateralData = (data) => ({
  propertyOwnerName: data.propertyOwnerName,
  propertyAddress: data.propertyAddress,
  propertyType: data.propertyType,
  landArea: data.landArea,
  constructionArea: data.constructionArea,
  valuation: data.valuation,
  valuationDate: data.valuationDate,
});

export const getDocumentsData = (data) => ({
  documents: data.documents,
});

export const getReviewData = (data) => ({
  confirmation: data.confirmation,
});
