const educationLoanSchema = {
  loanCode: "EL",
  title: "Education Loan Application",
  description: "Apply for an education loan to fund your higher education dreams",
  subtitle: "Support your educational journey with flexible financing options",
  category: "Education Finance",
  estimatedTime: "10-15 minutes",
  sections: [
    {
      sectionId: "student_details",
      sectionTitle: "Student Information",
      description: "Personal details of the student seeking education loan",
      iconName: "GraduationCap",
      iconColor: "text-blue-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "studentName",
          label: "Student Full Name",
          type: "text",
          required: true,
          placeholder: "Enter student's complete name",
          helpText: "Name as per educational certificates"
        },
        {
          name: "dateOfBirth",
          label: "Date of Birth",
          type: "date",
          required: true,
          helpText: "Student's date of birth as per official documents"
        },
        {
          name: "gender",
          label: "Gender",
          type: "radio",
          options: [
            { value: "male", label: "Male" },
            { value: "female", label: "Female" },
            { value: "other", label: "Other" }
          ],
          required: true
        },
        {
          name: "nationality",
          label: "Nationality",
          type: "select",
          options: [
            { value: "indian", label: "Indian" },
            { value: "nri", label: "Non-Resident Indian" },
            { value: "other", label: "Other" }
          ],
          required: true
        },
        {
          name: "contactInfo",
          label: "Contact Information",
          type: "group",
          gridClass: "grid-cols-2",
          colSpan: 2,
          fields: [
            {
              name: "phone",
              label: "Mobile Number",
              type: "tel",
              required: true,
              placeholder: "9876543210"
            },
            {
              name: "email",
              label: "Email Address",
              type: "email",
              required: true,
              placeholder: "<EMAIL>"
            }
          ]
        },
        {
          name: "address",
          label: "Permanent Address",
          type: "textarea",
          required: true,
          colSpan: 2,
          placeholder: "Enter complete permanent address",
          rows: 3
        }
      ]
    },
    {
      sectionId: "course_details",
      sectionTitle: "Course & Institution Details",
      description: "Information about the course and educational institution",
      iconName: "BookOpen",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "institutionName",
          label: "Institution/University Name",
          type: "text",
          required: true,
          placeholder: "Enter institution name",
          helpText: "Full name of the educational institution"
        },
        {
          name: "institutionLocation",
          label: "Institution Location",
          type: "text",
          required: true,
          placeholder: "City, State, Country"
        },
        {
          name: "courseType",
          label: "Course Type",
          type: "select",
          required: true,
          options: [
            { value: "undergraduate", label: "Undergraduate (UG)" },
            { value: "postgraduate", label: "Postgraduate (PG)" },
            { value: "diploma", label: "Diploma" },
            { value: "professional", label: "Professional Course" },
            { value: "phd", label: "PhD/Doctorate" }
          ]
        },
        {
          name: "courseName",
          label: "Course Name",
          type: "text",
          required: true,
          placeholder: "e.g., B.Tech Computer Science, MBA, etc."
        },
        {
          name: "courseDuration",
          label: "Course Duration (Years)",
          type: "number",
          required: true,
          min: 1,
          max: 10,
          placeholder: "4"
        },
        {
          name: "academicYear",
          label: "Academic Year of Admission",
          type: "text",
          required: true,
          placeholder: "2024-2025"
        }
      ]
    },
    {
      sectionId: "loan_details",
      sectionTitle: "Loan Requirements",
      description: "Specify the loan amount and expense breakdown",
      iconName: "DollarSign",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "totalLoanAmount",
          label: "Total Loan Amount Required (₹)",
          type: "number",
          required: true,
          placeholder: "1000000",
          min: 50000,
          max: 10000000,
          helpText: "Total amount needed for the entire course"
        },
        {
          name: "tuitionFees",
          label: "Annual Tuition Fees (₹)",
          type: "number",
          required: true,
          placeholder: "200000"
        },
        {
          name: "livingExpenses",
          label: "Annual Living Expenses (₹)",
          type: "number",
          placeholder: "100000",
          helpText: "Accommodation, food, and other living costs"
        },
        {
          name: "otherExpenses",
          label: "Other Educational Expenses (₹)",
          type: "number",
          placeholder: "50000",
          helpText: "Books, equipment, travel, etc."
        }
      ]
    },
    {
      sectionId: "guarantor_details",
      sectionTitle: "Co-applicant/Guarantor Details",
      description: "Information about the co-applicant or guarantor",
      iconName: "UserCheck",
      iconColor: "text-indigo-600",
      required: true,
      gridClass: "grid-cols-1 md:grid-cols-2",
      fields: [
        {
          name: "guarantorName",
          label: "Co-applicant/Guarantor Name",
          type: "text",
          required: true,
          placeholder: "Enter guarantor's full name"
        },
        {
          name: "relationshipWithStudent",
          label: "Relationship with Student",
          type: "select",
          required: true,
          options: [
            { value: "father", label: "Father" },
            { value: "mother", label: "Mother" },
            { value: "guardian", label: "Guardian" },
            { value: "spouse", label: "Spouse" },
            { value: "other", label: "Other" }
          ]
        },
        {
          name: "guarantorIncome",
          label: "Annual Income (₹)",
          type: "number",
          required: true,
          placeholder: "500000",
          helpText: "Gross annual income of the guarantor"
        },
        {
          name: "guarantorOccupation",
          label: "Occupation",
          type: "text",
          required: true,
          placeholder: "Enter occupation details"
        }
      ]
    },
    {
      sectionId: "declaration",
      sectionTitle: "Declaration",
      description: "Legal declarations and commitments",
      iconName: "CheckCircle",
      iconColor: "text-green-600",
      required: true,
      gridClass: "grid-cols-1",
      fields: [
        {
          name: "informationAccuracy",
          label: "I/We declare that all information provided is true and accurate",
          type: "checkbox",
          required: true
        },
        {
          name: "termsAcceptance",
          label: "I/We accept the terms and conditions of the education loan",
          type: "checkbox",
          required: true
        },
        {
          name: "repaymentCommitment",
          label: "I/We commit to repay the loan as per the agreed schedule",
          type: "checkbox",
          required: true
        }
      ]
    }
  ]
};

export default educationLoanSchema;
