import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_HOST } from "../../../constants";
import { getHeaders } from "../../header/requestHeader";
import {
  setLocation,
  setLocationError,
  setLocationLoading,
} from "../../reducer/location/location.js";
// import { getHeaders } from "../../utils/requestHeaders";

export const locationApi = createApi({
  reducerPath: "locationApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_HOST}`,
  }),

  endpoints: (builder) => ({
    getCountries: builder.mutation({
      query: () => ({
        url: `/countries`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setLocationLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          dispatch(
            setLocation({
              country: response.data.data,
            })
          );
        } catch (e) {
          console.log(">>>>>setBranch error", e);
          dispatch(setLocationError(e.message || "Failed to load branches"));
        }
      },
    }),

    getState: builder.mutation({
      query: (countryCode) => ({
        url: `/state/${countryCode}`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setLocationLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          dispatch(
            setLocation({
              state: response.data.data,
            })
          );
        } catch (e) {
          console.log(">>>>>setBranch error", e);
          dispatch(setLocationError(e.message || "Failed to load branches"));
        }
      },
    }),

    getCity: builder.mutation({
      query: (stateId) => ({
        url: `/city/${stateId}`,
        method: "GET",
        headers: getHeaders(),
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        dispatch(setLocationLoading()); // Set loading to true before the request

        try {
          const response = await queryFulfilled;
          dispatch(
            setLocation({
              city: response.data.data,
            })
          );
        } catch (e) {
          console.log(">>>>>setBranch error", e);
          dispatch(setLocationError(e.message || "Failed to load branches"));
        }
      },
    }),
  }),
});
// Export hooks for usage in functional components
export const {
  useGetCountriesMutation,
  useGetCityMutation,
  useGetStateMutation,
} = locationApi;
