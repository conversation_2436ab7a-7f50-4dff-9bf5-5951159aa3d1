import { ThreeDots } from "react-loader-spinner";

// eslint-disable-next-line react/prop-types
const Loader = ({ visible }) => {
  return (
    <div className="flex flex-col h-full">
      <div className="flex flex-1 justify-center items-center">
        <ThreeDots
          height="50"
          width="50"
          color="var(--color-primary-color)"
          ariaLabel="tail-spin-loading"
          radius="1"
          wrapperStyle={{}}
          wrapperClass=""
          visible={visible}
        />
      </div>
    </div>
  );
};

export default Loader;
