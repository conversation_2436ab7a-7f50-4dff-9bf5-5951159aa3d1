import { Separator } from "@/components/ui/separator";

const SidebarHeaderLogo = () => {
  return (
    <div className="relative">
      {/* Collapsed state */}
      <div className="hidden group-data-[collapsible=icon]:block">
        <div className="flex flex-col items-center">
          <div className="py-5 flex justify-center">
            <div className="relative p-2 rounded-xl bg-gradient-to-br from-primary-color/10 to-sidebar-active-color/5 shadow-sm border border-primary-color/10">
              <img
                src="/src/assets/rpc-logo.png"
                alt="Logo"
                className="w-8 h-8 object-contain"
              />
            </div>
          </div>
          <Separator className="w-full opacity-30" />
        </div>
      </div>

      {/* Expanded state */}
      <div className="block group-data-[collapsible=icon]:hidden">
        <div className="flex flex-row gap-4 px-6 py-5 items-center relative">
          <div className="relative p-2 rounded-xl bg-gradient-to-br from-primary-color/10 to-sidebar-active-color/5 shadow-sm border border-primary-color/10">
            <img
              src="/src/assets/rpc-logo.png"
              alt="Logo"
              className="w-8 h-8 object-contain"
            />
          </div>
          <div className="flex flex-col">
            <span className="text-logo-color text-xl font-bold tracking-wide bg-gradient-to-r from-logo-color to-primary-color bg-clip-text">
              RPC BANK
            </span>
            <span className="text-xs text-gray-500 font-medium tracking-wider uppercase">
              Loan Management
            </span>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent pointer-events-none" />
        </div>
        <Separator className="w-full opacity-30" />
      </div>
    </div>
  );
};

export default SidebarHeaderLogo;
