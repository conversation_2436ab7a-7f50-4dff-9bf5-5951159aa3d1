import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import { setApiError } from "../../hooks/errorMessage";
import { ROUTES } from "../../constants/route";
import InputField from "../../components/custom/InputField";
import SelectInput from "../../components/custom/SelectInput";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "../../components/ui/form";
import { Input } from "../../components/ui/input";
import { Button } from "../../components/ui/button";
import { ROLES_DROPDOWN } from "../../constants/roles";
import FormSelect from "../../components/custom/SelectInput";
import { useEffect } from "react";
import { useAddUserMutation, useGetBranchMutation } from "../../store/actions";
import { useSelector } from "react-redux";
import { mapToLabelValueArray } from "../../lib/utils";

const formSchema = z.object({
  branchId: z.string().min(1),
  fname: z.string().min(1).max(40),
  mname: z.string().optional(),
  lname: z.string().min(1).max(40),
  phoneCode: z.string().min(1),
  phoneNumber: z.string().min(1).max(10),
  email: z.string().email().min(1),
  role: z.string().min(1),
});

const AddUser = () => {
  const navigate = useNavigate();

  const [getBranch] = useGetBranchMutation();

  useEffect(() => {
    getBranch({ limit: 100000 });
  }, []);

  const { data: branchData } = useSelector((state) => state.branch);

  const branches = mapToLabelValueArray(branchData, "branch_name", "_id");

  console.log("branches >>> ", branchData);

  const [addUser, { isLoading: addUserLoading }] = useAddUserMutation();

  const rolesData = ROLES_DROPDOWN;

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      branchId: "",
      fname: "",
      mname: "",
      lname: "",
      phoneCode: "+91",
      phoneNumber: "",
      email: "",
      role: "",
    },
  });

  function onSubmit(values) {
    const finalValues = {
      branch_id: values.branchId,
      fname: values.fname,
      mname: values.mname,
      lname: values.lname,
      phone_code: values.phoneCode,
      phone_number: values.phoneNumber,
      email: values.email,
      role: values.role,
    };
    if (!addUserLoading) {
      addUser(finalValues).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          navigate(ROUTES.USER_MANAGEMENT);
        }
      });
    }
  }

  return (
    <div className="p-8 flex-col flex-1 h-screen overflow-y-hidden">
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-6 pb-2">
              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <SelectInput
                    label="Select Branch"
                    name="branchId"
                    control={form.control}
                    isRequired
                    options={branches}
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="fname"
                    label="First Name"
                    placeholder="Enter First Name"
                    isRequired
                  />
                </div>
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="mname"
                    label="Middle Name"
                    placeholder="Enter Middle Name"
                  />
                </div>
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="lname"
                    label="Last Name"
                    placeholder="Enter Last Name"
                    isRequired
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="email"
                    label="Email"
                    placeholder="Enter Email"
                    isRequired
                  />
                </div>

                <FormItem className="flex flex-col flex-1">
                  <FormLabel>
                    Phone Number <span className="text-red-500">*</span>
                  </FormLabel>
                  <div className="flex flex-row flex-1 gap-2.5">
                    <div className="w-16">
                      <FormField
                        control={form.control}
                        name="phoneCode"
                        render={({ field }) => (
                          <FormControl>
                            <Input
                              disabled
                              placeholder="Phone Code"
                              className="h-[40px]"
                              {...field}
                            />
                          </FormControl>
                        )}
                      />
                    </div>
                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter Phone Number"
                            className="h-[40px]"
                            {...field}
                          />
                        </FormControl>
                      )}
                    />
                  </div>
                </FormItem>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <FormSelect
                    label="Role"
                    name="role"
                    isRequired
                    control={form.control}
                    options={rolesData}
                  />
                </div>
              </div>

              <div className="flex items-center justify-center">
                <Button type="submit">Submit</Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default AddUser;
