import { useEffect, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { User, Wallet, Shield, FileText, CheckCircle } from "lucide-react";
import PersonalInfo from "./PersonalInfo";
import ProgressBar from "../ProgressBar/ProgressBar";
import LoanDetails from "./LoanDetails";
import { getSchemaForStep } from "../../utils/getStepFormSchema";
import {
  useAddCollateralDetailsMutation,
  useAddDocumentDetailsMutation,
  useAddLoanDetailsMutation,
  useAddPersonalInfoMutation,
  useUpdateApplicationStatusMutation,
} from "../../store/actions";
import { setApiError } from "../../hooks/errorMessage";
import { DEFAULT_VALUES } from "./primaryApplicationDefaultValues";
import {
  getCollateralData,
  getDocumentsData,
  getLoanDetailsData,
  getPersonalInfoData,
  getReviewData,
} from "./formSubmissionData";
import CollateralDetails from "./ColatrolDetaiils";
import { usePromptWithDialog } from "../../hooks/usePromptWithDialog";
import ReviewPage from "./ReviewPage";
import DocumentDetails from "./DocumentDetails";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "../../constants/route";

const PrimaryApplication = () => {
  const initialSteps = [
    { label: "Personal Info", icon: <User />, completed: false },
    { label: "Loan Details", icon: <Wallet />, completed: false },
    { label: "Collateral", icon: <Shield />, completed: false },
    { label: "Documents", icon: <FileText />, completed: false },
    { label: "Review", icon: <CheckCircle />, completed: false },
  ];

  const navigate = useNavigate();

  const [steps, setSteps] = useState(initialSteps);
  const [currentStep, setCurrentStep] = useState(0);

  const [personalInfoApi] = useAddPersonalInfoMutation();
  const [loanDetailsApi] = useAddLoanDetailsMutation();
  const [collateralApi] = useAddCollateralDetailsMutation();
  const [documentApi] = useAddDocumentDetailsMutation();

  const [updateApplicationStatus] = useUpdateApplicationStatusMutation();

  const personalInfoId = localStorage.getItem("personalInfoId");

  const methods = useForm({
    resolver: zodResolver(getSchemaForStep(currentStep)),
    defaultValues: DEFAULT_VALUES,
  });

  const {
    control,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors, isDirty },
  } = methods;

  const { DialogComponent, disablePromptForNextNavigation } =
    usePromptWithDialog(isDirty);

  const progressNext = () => {
    setSteps((prevSteps) =>
      prevSteps.map((step, index) =>
        index === currentStep ? { ...step, completed: true } : step
      )
    );
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      alert("Form submitted successfully!");
    }
  };

  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (isDirty) {
        e.preventDefault();
        e.returnValue = ""; // Required for Chrome
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [isDirty]);

  const onSubmit = (data) => {
    const allData = getValues();
    console.log("allData >> ", data);
    let formattedData;

    switch (currentStep) {
      case 0:
        formattedData = getPersonalInfoData(allData);
        break;
      case 1:
        formattedData = getLoanDetailsData(data);
        break;
      case 2:
        formattedData = getCollateralData(data);
        break;
      case 3:
        formattedData = getDocumentsData(data);
        break;
      case 4:
        formattedData = getReviewData(data);
        break;
      default:
        formattedData = {};
    }

    if (currentStep === 0) {
      if (!personalInfoId) {
        personalInfoApi(formattedData).then((res) => {
          if (res.error) {
            setApiError(res);
          } else {
            localStorage.setItem("personalInfoId", res.data.data._id);
            progressNext();
          }
        });
      } else {
        progressNext();
      }
    }

    const primaryApplicationId = localStorage.getItem("personalInfoId");

    if (currentStep === 1) {
      const finalData = { primaryApplicationId: primaryApplicationId, ...data };
      loanDetailsApi(finalData).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          localStorage.setItem("loanDetailsId", res.data.data._id);
          progressNext();
        }
      });
    }

    if (currentStep === 2) {
      const finalData = {
        primaryApplicationId: primaryApplicationId,
        ...data,
      };
      collateralApi(finalData).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          localStorage.setItem("collateralDetailsId", res.data.data._id);
          progressNext();
        }
      });
    }

    if (currentStep === 3) {
      const finalData = {
        primaryApplicationId: primaryApplicationId,
        ...data,
      };
      documentApi(finalData).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          localStorage.setItem("documentDetailsId", res.data.data._id);
          progressNext();
        }
      });
    }

    if (currentStep === 4) {
      const finalData = {
        primaryApplicationId: primaryApplicationId,
        userData: { status: 2 },
      };
      disablePromptForNextNavigation();
      updateApplicationStatus(finalData).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          localStorage.removeItem("documentDetailsId");
          localStorage.removeItem("collateralDetailsId");
          localStorage.removeItem("loanDetailsId");
          localStorage.removeItem("personalInfoId");
          navigate(ROUTES.PRIMARY_APPLICATION_LIST);
        }
      });
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setSteps((prevSteps) =>
        prevSteps.map((step, index) =>
          index === currentStep - 1 ? { ...step, completed: false } : step
        )
      );
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <PersonalInfo
            control={control}
            errors={errors}
            getValues={getValues}
            setValue={setValue}
          />
        );
      case 1:
        return (
          <LoanDetails
            control={control}
            errors={errors}
            getValues={getValues}
            setValue={setValue}
          />
        );
      case 2:
        return (
          <CollateralDetails
            control={control}
            errors={errors}
            getValues={getValues}
            setValue={setValue}
          />
        );
      case 3:
        return (
          <DocumentDetails
            control={control}
            errors={errors}
            getValues={getValues}
            setValue={setValue}
          />
        );
      case 4:
        return <ReviewPage />;
      default:
        return null;
    }
  };

  return (
    <div className="p-6 flex-col flex-1 h-screen overflow-y-hidden relative">
      {/* Main Content */}
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <ProgressBar steps={steps} currentStep={currentStep} />
        <Separator className="my-6 bg-gray-200/50" />
        <div className="mt-6">
          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)}>
              {renderStepContent()}
              <div className="flex flex-row w-full gap-4 items-center mt-8 pt-6 border-t border-gray-200/50">
                <div className="flex flex-1 justify-start">
                  {currentStep > 0 && (
                    <Button
                      type="button"
                      onClick={handleBack}
                      variant="outline"
                      className="px-6 py-2.5 rounded-xl border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
                    >
                      Back
                    </Button>
                  )}
                </div>
                <div className="flex flex-1 justify-end">
                  <Button
                    type="submit"
                    className="bg-gradient-to-r from-primary-color to-sidebar-active-color hover:from-primary-color/90 hover:to-sidebar-active-color/90 text-white font-semibold px-8 py-2.5 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    {currentStep === steps.length - 1
                      ? "Submit Application"
                      : "Continue"}
                  </Button>
                </div>
              </div>
            </form>
          </FormProvider>
          <DialogComponent />
        </div>
      </div>
    </div>
  );
};

export default PrimaryApplication;
