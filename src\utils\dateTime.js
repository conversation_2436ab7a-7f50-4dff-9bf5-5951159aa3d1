export const getLastFiveFinancialYears = () => {
  const years = [];
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  let startYear = currentMonth >= 4 ? currentYear : currentYear - 1;

  for (let i = 0; i < 5; i++) {
    let endYearShort = (startYear + 1).toString();
    years.push(`${startYear}-${endYearShort}`);
    startYear--;
  }

  return years;
};

import { format, formatInTimeZone } from "date-fns-tz";

const TIMEZONE = "Asia/Kolkata"; // or your preferred timezone

export const dateTimeZone = (date) => {
  if (date === undefined || date === null) return null;

  try {
    const utcDate = formatInTimeZone(date, TIMEZONE, "MM-dd-yyyy hh:mm a");
    return utcDate;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const dateFormat = (date) => {
  if (date === undefined || date === null) return null;

  try {
    const utcDate = format(date, "MM-dd-yyyy");
    return utcDate;
  } catch (error) {
    console.error(error);
    return null;
  }
};
