/* eslint-disable react/prop-types */
import {
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
  SelectGroup,
  SelectLabel,
  SelectSeparator,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

export default function SelectInput({
  label,
  name,
  control,
  options,
  hideLabel,
  isRequired = false,
  disabled,
}) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem>
          {!hideLabel && (
            <FormLabel>
              {label}{" "}
              {isRequired ? <span className="text-red-500">*</span> : ""}
            </FormLabel>
          )}
          <FormControl>
            <Select onValueChange={field.onChange} value={field.value}>
              <SelectTrigger
                className={cn(
                  "w-full h-[40px] border-input",
                  fieldState.error && "border-destructive ring-destructive"
                )}
                aria-invalid={!!fieldState.error}
                disabled={disabled}
              >
                <SelectValue placeholder={`Select ${label}`} />
              </SelectTrigger>
              <SelectContent>
                {options.map((option, index) => {
                  const isLast = index === options.length - 1;
                  const isGroup = option.data && Array.isArray(option.data);

                  if (isGroup) {
                    return (
                      <div key={option.name}>
                        <SelectGroup>
                          <SelectLabel>{option.name}</SelectLabel>
                          {option.data.map((item) => (
                            <SelectItem key={item.value} value={item.value}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                        {!isLast && <SelectSeparator />}
                      </div>
                    );
                  }

                  return (
                    <SelectItem key={option.value} value={option.value}>
                      {option.name}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
