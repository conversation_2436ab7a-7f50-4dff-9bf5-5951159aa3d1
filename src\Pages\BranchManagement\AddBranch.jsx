import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { setApiError } from "../../hooks/errorMessage";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { useAddBranchMutation } from "../../store/actions";
import { ROUTES } from "../../constants/route";
import InputField from "../../components/custom/InputField";
import TextAreaField from "../../components/custom/TextAreaField";

const formSchema = z.object({
  branchName: z.string().min(1).max(50),
  branchCode: z.string().min(1).max(8),
  branchAddress: z.string().min(1),
  // branchManager: z.string().min(1).max(40),
  // branchManagerId: z.string().min(1).max(8),
  branchStatus: z.string().optional(),
});

const AddBranch = () => {
  const navigate = useNavigate();

  const [addBranch, { isLoading: addBranchLoader }] = useAddBranchMutation();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      branchName: "",
      branchCode: "",
      branchAddress: "",
      // branchManager: "",
      // branchManagerId: "",
    },
  });

  function onSubmit(values) {
    const finalValues = {
      branch_name: values.branchName,
      branch_code: values.branchCode,
      branch_address: values.branchAddress,
      // branch_manager: values.branchManager,
      // branch_manager_id: values.branchManagerId,
      branch_status: "pending",
    };
    if (!addBranchLoader) {
      addBranch(finalValues).then((res) => {
        if (res.error) {
          setApiError(res);
        } else {
          navigate(ROUTES.BRANCH_MANAGEMENT);
        }
      });
    }
  }

  return (
    <div className="p-8 flex-col flex-1 h-screen overflow-y-hidden">
      <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl border border-gray-200/50 shadow-lg">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-6 pb-2">
              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="branchName"
                    label="Branch Name"
                    placeholder="Enter Branch Name"
                    isRequired
                  />
                </div>
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="branchCode"
                    label="Branch Code"
                    placeholder="Enter Branch Code"
                    isRequired
                  />
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <TextAreaField
                    control={form.control}
                    name={"branchAddress"}
                    label={"Branch Address"}
                    placeholder="Enter Branch Address"
                    className="resize-none"
                  />
                </div>
              </div>

              {/* <div className="flex flex-col md:flex-row gap-2.5">
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="branchManager"
                    label="Branch Manager Name"
                    placeholder="Enter Branch Manager Name"
                    isRequired
                  />
                </div>
                <div className="flex-1">
                  <InputField
                    control={form.control}
                    name="branchManagerId"
                    label="Branch Manager Id"
                    placeholder="Enter Branch Manager Id"
                    isRequired
                  />
                </div>
              </div> */}

              <div className="flex items-center justify-center">
                <Button type="submit">Submit</Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default AddBranch;
