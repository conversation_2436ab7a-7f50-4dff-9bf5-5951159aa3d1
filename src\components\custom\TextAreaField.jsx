/* eslint-disable react/prop-types */
"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";

export default function TextAreaField({
  control,
  name,
  label,
  placeholder = "",
  className = "",
}) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <div className="flex flex-row gap-3">
            <FormLabel htmlFor={name}>{label}</FormLabel>
            <FormMessage />
          </div>
          <FormControl>
            <Textarea
              id={name}
              placeholder={placeholder}
              className={className}
              {...field}
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
}
