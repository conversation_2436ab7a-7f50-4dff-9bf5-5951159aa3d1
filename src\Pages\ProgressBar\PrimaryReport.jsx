import ProgressBar from "./ProgressBar";
import { useState } from "react";
import { User, Wallet, Shield, FileText, CheckCircle } from "lucide-react";

const initialSteps = [
  { label: "Personal Info", icon: <User />, completed: true },
  { label: "Loan Details", icon: <Wallet />, completed: false },
  { label: "Collateral", icon: <Shield />, completed: false },
  { label: "Documents", icon: <FileText />, completed: false },
  { label: "Review", icon: <CheckCircle />, completed: false },
];

const PrimaryReport = () => {
  const [steps, setSteps] = useState(initialSteps);
  const [currentStep, setCurrentStep] = useState(0);

  const handleSubmit = () => {
    const updatedSteps = steps.map((step, index) =>
      index === currentStep ? { ...step, completed: true } : step
    );
    setSteps(updatedSteps);

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      const updatedSteps = steps.map((step, index) =>
        index === currentStep - 1 ? { ...step, completed: false } : step
      );
      setSteps(updatedSteps);
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="p-5">
      <ProgressBar steps={steps} currentStep={currentStep} />

      <div className="mt-8 flex gap-4">
        <button
          onClick={handleBack}
          className="px-4 py-2 bg-gray-500 text-white rounded-xl hover:bg-gray-600"
          disabled={currentStep === 0}
        >
          Back
        </button>

        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-blue-500 text-white rounded-xl hover:bg-blue-600"
        >
          Submit Step
        </button>
      </div>
    </div>
  );
};

export default PrimaryReport;
