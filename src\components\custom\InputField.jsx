/* eslint-disable react/prop-types */
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";

function InputField({
  control,
  name,
  label,
  placeholder,
  isRequired = false,
  className = "",
  type,
  disabled = false,
}) {
  const [showPassword, setShowPassword] = useState(false);
  const isPassword = type === "password";
  const isNewPassword = name === "newPassword";

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem>
          {label && (
            <div className="flex flex-row gap-3">
              <FormLabel htmlFor={field.name}>
                {label}{" "}
                {isRequired ? <span className="text-red-500">*</span> : ""}
              </FormLabel>
            </div>
          )}
          <FormControl>
            <div className="relative">
              <Input
                id={field.name}
                className={"h-[40px] pr-10 text-sm" + className}
                placeholder={placeholder}
                type={isPassword && !showPassword ? "password" : "text"}
                disabled={disabled}
                aria-invalid={!!fieldState.error}
                ref={field.ref}
                {...field}
              />
              {isPassword && (
                <button
                  type="button"
                  onClick={() => setShowPassword((prev) => !prev)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              )}
            </div>
          </FormControl>
          {isNewPassword && (
            <i className="pt-[-20px] text-xs leading-tight text-gray-500">
              Password must contain 1 number, 1 special character, 1 lowercase
              letter, 1 uppercase letter, and be at least 10 characters long.
            </i>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export default InputField;
